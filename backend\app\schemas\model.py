"""
Pydantic schemas for ML model operations.
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel


class MLModelBase(BaseModel):
    """Base ML model schema."""
    name: str
    description: Optional[str] = None
    model_type: str  # classification, regression, clustering
    algorithm: str  # random_forest, svm, kmeans, etc.


class MLModelCreate(MLModelBase):
    """Schema for ML model creation."""
    dataset_id: int
    target_column: Optional[str] = None
    feature_columns: Optional[List[str]] = None
    hyperparameters: Optional[Dict[str, Any]] = None


class MLModelUpdate(BaseModel):
    """Schema for ML model updates."""
    name: Optional[str] = None
    description: Optional[str] = None


class MLModel(MLModelBase):
    """ML model schema."""
    id: int
    dataset_id: int
    target_column: Optional[str]
    feature_columns: Optional[List[str]]
    training_score: Optional[float]
    validation_score: Optional[float]
    test_score: Optional[float]
    metrics: Optional[Dict[str, Any]]
    hyperparameters: Optional[Dict[str, Any]]
    training_time_seconds: Optional[float]
    status: str
    training_error: Optional[str]
    owner_id: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class ModelTrainingRequest(BaseModel):
    """Schema for model training request."""
    hyperparameters: Optional[Dict[str, Any]] = None
    validation_split: float = 0.2
    test_split: float = 0.2
    cross_validation: bool = True
    cv_folds: int = 5


class ModelPredictionRequest(BaseModel):
    """Schema for model prediction request."""
    input_data: Dict[str, Any]


class ModelPredictionResponse(BaseModel):
    """Schema for model prediction response."""
    prediction_id: int
    prediction: Any
    confidence_score: Optional[float]
    model_id: int


class ModelPrediction(BaseModel):
    """Model prediction schema."""
    id: int
    model_id: int
    input_data: Dict[str, Any]
    prediction: Any
    confidence_score: Optional[float]
    prediction_time: datetime
    
    class Config:
        from_attributes = True


class ModelMetrics(BaseModel):
    """Model performance metrics schema."""
    accuracy: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    f1_score: Optional[float] = None
    roc_auc: Optional[float] = None
    mse: Optional[float] = None
    rmse: Optional[float] = None
    mae: Optional[float] = None
    r2_score: Optional[float] = None
    confusion_matrix: Optional[List[List[int]]] = None
    feature_importance: Optional[Dict[str, float]] = None


class ModelTrainingResult(BaseModel):
    """Model training result schema."""
    model_id: int
    status: str
    training_score: Optional[float]
    validation_score: Optional[float]
    test_score: Optional[float]
    metrics: Optional[ModelMetrics]
    training_time_seconds: float
    model_file_path: str
