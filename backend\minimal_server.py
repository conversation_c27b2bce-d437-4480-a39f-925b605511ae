#!/usr/bin/env python3
"""
Minimal FastAPI server for testing without dependencies.
"""
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import uvicorn

# Create FastAPI app
app = FastAPI(title="Diorite API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class UserCreate(BaseModel):
    email: str
    username: str
    password: str
    full_name: Optional[str] = None

class User(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool = True

# Routes
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0", "environment": "development"}

@app.post("/api/v1/auth/login", response_model=Token)
async def login(login_data: LoginRequest):
    # Simple auth for demo - accept any username/password
    if login_data.username and login_data.password:
        return Token(
            access_token="demo-token-123",
            refresh_token="demo-refresh-token-456",
            token_type="bearer"
        )
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

@app.post("/api/v1/auth/register", response_model=User)
async def register(user_data: UserCreate):
    return User(
        id=1,
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        is_active=True
    )

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user_info():
    return User(
        id=1,
        username="demo_user",
        email="<EMAIL>",
        full_name="Demo User",
        is_active=True
    )

# Dataset endpoints
@app.get("/api/v1/datasets/")
async def get_datasets():
    return {
        "datasets": [
            {
                "id": 1,
                "name": "Sample Sales Data",
                "original_filename": "sales_data.csv",
                "file_size": 1024000,
                "rows_count": 1000,
                "columns_count": 8,
                "processing_status": "processed",
                "created_at": "2024-01-01T00:00:00Z",
                "columns_info": [
                    {"name": "date", "dtype": "object"},
                    {"name": "product", "dtype": "object"},
                    {"name": "sales", "dtype": "float64"},
                    {"name": "quantity", "dtype": "int64"},
                    {"name": "price", "dtype": "float64"},
                    {"name": "category", "dtype": "object"},
                    {"name": "region", "dtype": "object"},
                    {"name": "customer_id", "dtype": "int64"}
                ]
            },
            {
                "id": 2,
                "name": "Customer Demographics",
                "original_filename": "customers.csv",
                "file_size": 512000,
                "rows_count": 500,
                "columns_count": 6,
                "processing_status": "processed",
                "created_at": "2024-01-02T00:00:00Z",
                "columns_info": [
                    {"name": "customer_id", "dtype": "int64"},
                    {"name": "age", "dtype": "int64"},
                    {"name": "gender", "dtype": "object"},
                    {"name": "income", "dtype": "float64"},
                    {"name": "city", "dtype": "object"},
                    {"name": "signup_date", "dtype": "object"}
                ]
            }
        ]
    }

@app.get("/api/v1/datasets/{dataset_id}")
async def get_dataset(dataset_id: int):
    if dataset_id == 1:
        return {
            "id": 1,
            "name": "Sample Sales Data",
            "original_filename": "sales_data.csv",
            "file_size": 1024000,
            "rows_count": 1000,
            "columns_count": 8,
            "processing_status": "processed",
            "created_at": "2024-01-01T00:00:00Z",
            "columns_info": [
                {"name": "date", "dtype": "object"},
                {"name": "product", "dtype": "object"},
                {"name": "sales", "dtype": "float64"},
                {"name": "quantity", "dtype": "int64"},
                {"name": "price", "dtype": "float64"},
                {"name": "category", "dtype": "object"},
                {"name": "region", "dtype": "object"},
                {"name": "customer_id", "dtype": "int64"}
            ]
        }
    elif dataset_id == 2:
        return {
            "id": 2,
            "name": "Customer Demographics",
            "original_filename": "customers.csv",
            "file_size": 512000,
            "rows_count": 500,
            "columns_count": 6,
            "processing_status": "processed",
            "created_at": "2024-01-02T00:00:00Z",
            "columns_info": [
                {"name": "customer_id", "dtype": "int64"},
                {"name": "age", "dtype": "int64"},
                {"name": "gender", "dtype": "object"},
                {"name": "income", "dtype": "float64"},
                {"name": "city", "dtype": "object"},
                {"name": "signup_date", "dtype": "object"}
            ]
        }
    else:
        raise HTTPException(status_code=404, detail="Dataset not found")

@app.get("/api/v1/chat/sessions")
async def get_chat_sessions():
    return {"sessions": [], "message": "No chat sessions yet - this is a test endpoint"}

# Manual Analysis endpoints
@app.post("/api/v1/manual-analysis/clean-data")
async def clean_data_manually(request: dict):
    return {
        "success": True,
        "original_shape": [1000, 10],
        "final_shape": [950, 10],
        "cleaning_summary": [
            {"operation": "remove_duplicates", "removed_rows": 50}
        ],
        "message": "Data cleaning completed successfully (demo response)"
    }

@app.post("/api/v1/manual-analysis/create-visualization")
async def create_manual_visualization(request: dict):
    # Mock plot data for demo
    mock_plot_data = {
        "data": [
            {
                "x": [1, 2, 3, 4, 5],
                "y": [2, 4, 3, 5, 6],
                "type": "scatter",
                "mode": "markers",
                "name": "Sample Data"
            }
        ],
        "layout": {
            "title": "Sample Visualization",
            "xaxis": {"title": "X Axis"},
            "yaxis": {"title": "Y Axis"}
        }
    }

    return {
        "success": True,
        "chart_data": {"data": mock_plot_data},
        "dataset_info": {
            "name": "Sample Dataset",
            "filtered_rows": 100,
            "total_rows": 100
        }
    }

@app.post("/api/v1/manual-analysis/train-model")
async def train_model_manually(request: dict):
    return {
        "success": True,
        "algorithm": "random_forest",
        "training_score": 0.95,
        "validation_score": 0.87,
        "training_time_seconds": 2.5,
        "message": "Model training completed successfully (demo response)"
    }

@app.post("/api/v1/manual-analysis/statistical-analysis")
async def perform_statistical_analysis(request: dict):
    return {
        "success": True,
        "analysis_type": "summary",
        "results": {
            "column1": {
                "count": 100,
                "mean": 50.5,
                "std": 15.2,
                "min": 10,
                "max": 90
            }
        },
        "dataset_info": {
            "name": "Sample Dataset",
            "analyzed_columns": ["column1"]
        }
    }

@app.get("/api/v1/manual-analysis/analysis-templates")
async def get_analysis_templates():
    return {
        "templates": {
            "data_cleaning": {
                "name": "Standard Data Cleaning",
                "operations": [
                    {"type": "remove_duplicates"},
                    {"type": "handle_missing", "method": "drop"}
                ]
            }
        }
    }

if __name__ == "__main__":
    print("🚀 Starting Diorite Minimal Backend Server...")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔑 Demo login: any username/password will work")
    uvicorn.run(app, host="0.0.0.0", port=8000)
