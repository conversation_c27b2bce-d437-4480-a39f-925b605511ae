"""
Celery tasks for ML model training.
"""
from celery import current_task
from sqlalchemy.orm import Session

from app.tasks.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.model import MLModel
from app.models.dataset import Dataset
from app.services.data_processor import data_processor
from app.services.ml_service import ml_service
import structlog

logger = structlog.get_logger()


@celery_app.task(bind=True)
def train_model_task(self, model_id: int, training_params: dict):
    """Train a machine learning model."""
    db: Session = SessionLocal()
    
    try:
        # Update task status
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "status": "Starting model training"}
        )
        
        # Get model and dataset
        model = db.query(MLModel).filter(MLModel.id == model_id).first()
        if not model:
            raise Exception(f"Model {model_id} not found")
        
        dataset = db.query(Dataset).filter(Dataset.id == model.dataset_id).first()
        if not dataset:
            raise Exception(f"Dataset {model.dataset_id} not found")
        
        logger.info("Starting model training", model_id=model_id, algorithm=model.algorithm)
        
        # Load dataset
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 20, "total": 100, "status": "Loading dataset"}
        )
        
        df = data_processor.load_dataset(dataset.file_path)
        
        # Train model
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 40, "total": 100, "status": "Training model"}
        )
        
        training_result = ml_service.train_model(
            df=df,
            algorithm=model.algorithm,
            model_type=model.model_type,
            target_column=model.target_column,
            feature_columns=model.feature_columns,
            hyperparameters=model.hyperparameters,
            validation_split=training_params.get("validation_split", 0.2),
            test_split=training_params.get("test_split", 0.2),
            user_id=model.owner_id
        )
        
        # Update model with results
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 90, "total": 100, "status": "Saving results"}
        )
        
        model.model_file_path = training_result["model_path"]
        model.training_score = training_result.get("training_score")
        model.validation_score = training_result.get("validation_score")
        model.test_score = training_result.get("test_score")
        model.metrics = training_result.get("metrics")
        model.training_time_seconds = training_result["training_time_seconds"]
        model.status = "completed"
        model.training_error = None
        
        # Update feature columns if they were auto-selected
        if training_result.get("feature_columns"):
            model.feature_columns = training_result["feature_columns"]
        
        db.commit()
        
        logger.info("Model training completed", 
                   model_id=model_id, 
                   training_score=model.training_score,
                   validation_score=model.validation_score)
        
        return {
            "status": "completed",
            "model_id": model_id,
            "training_score": model.training_score,
            "validation_score": model.validation_score,
            "test_score": model.test_score,
            "training_time": model.training_time_seconds
        }
        
    except Exception as e:
        logger.error("Model training failed", model_id=model_id, error=str(e))
        
        # Update error status
        if model:
            model.status = "failed"
            model.training_error = str(e)
            db.commit()
        
        raise
    
    finally:
        db.close()


@celery_app.task(bind=True)
def evaluate_model_task(self, model_id: int, test_dataset_id: int):
    """Evaluate a trained model on a test dataset."""
    db: Session = SessionLocal()
    
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "status": "Starting model evaluation"}
        )
        
        # Get model and test dataset
        model = db.query(MLModel).filter(MLModel.id == model_id).first()
        if not model:
            raise Exception(f"Model {model_id} not found")
        
        test_dataset = db.query(Dataset).filter(Dataset.id == test_dataset_id).first()
        if not test_dataset:
            raise Exception(f"Test dataset {test_dataset_id} not found")
        
        if model.status != "completed":
            raise Exception("Model is not trained yet")
        
        logger.info("Starting model evaluation", model_id=model_id, test_dataset_id=test_dataset_id)
        
        # Load test dataset
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 30, "total": 100, "status": "Loading test dataset"}
        )
        
        test_df = data_processor.load_dataset(test_dataset.file_path)
        
        # Make predictions on test dataset
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 60, "total": 100, "status": "Making predictions"}
        )
        
        predictions = []
        for _, row in test_df.iterrows():
            input_data = row.to_dict()
            if model.target_column and model.target_column in input_data:
                del input_data[model.target_column]  # Remove target from input
            
            pred_result = ml_service.predict(model.model_file_path, input_data)
            predictions.append(pred_result)
        
        logger.info("Model evaluation completed", 
                   model_id=model_id, 
                   predictions_count=len(predictions))
        
        return {
            "status": "completed",
            "model_id": model_id,
            "test_dataset_id": test_dataset_id,
            "predictions_count": len(predictions),
            "sample_predictions": predictions[:5]  # Return first 5 predictions as sample
        }
        
    except Exception as e:
        logger.error("Model evaluation failed", model_id=model_id, error=str(e))
        raise
    
    finally:
        db.close()
