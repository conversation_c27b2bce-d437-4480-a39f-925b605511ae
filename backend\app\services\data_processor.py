"""
Data processing service for dataset analysis and manipulation.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import json
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer

from app.schemas.dataset import DataCleaningOptions


class DataProcessor:
    """Data processing and analysis service."""
    
    def __init__(self):
        self.supported_formats = ['.csv', '.xlsx', '.xls', '.json']
    
    def load_dataset(self, file_path: str) -> pd.DataFrame:
        """Load dataset from file."""
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        if extension == '.csv':
            return pd.read_csv(file_path)
        elif extension in ['.xlsx', '.xls']:
            return pd.read_excel(file_path)
        elif extension == '.json':
            return pd.read_json(file_path)
        else:
            raise ValueError(f"Unsupported file format: {extension}")
    
    def get_dataset_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get basic information about the dataset."""
        return {
            "shape": df.shape,
            "columns": df.columns.tolist(),
            "dtypes": df.dtypes.astype(str).to_dict(),
            "memory_usage": df.memory_usage(deep=True).sum(),
            "missing_values": df.isnull().sum().to_dict(),
            "duplicate_rows": df.duplicated().sum()
        }
    
    def get_column_statistics(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """Get detailed statistics for each column."""
        stats = {}
        
        for column in df.columns:
            col_stats = {
                "dtype": str(df[column].dtype),
                "non_null_count": df[column].count(),
                "null_count": df[column].isnull().sum(),
                "unique_count": df[column].nunique(),
            }
            
            if df[column].dtype in ['int64', 'float64']:
                # Numerical statistics
                col_stats.update({
                    "mean": df[column].mean(),
                    "std": df[column].std(),
                    "min": df[column].min(),
                    "max": df[column].max(),
                    "median": df[column].median(),
                    "q25": df[column].quantile(0.25),
                    "q75": df[column].quantile(0.75),
                })
            else:
                # Categorical statistics
                col_stats.update({
                    "most_frequent": df[column].mode().iloc[0] if not df[column].mode().empty else None,
                    "frequency": df[column].value_counts().head(10).to_dict()
                })
            
            stats[column] = col_stats
        
        return stats
    
    def get_data_preview(self, df: pd.DataFrame, sample_size: int = 100) -> Dict[str, Any]:
        """Get a preview of the dataset."""
        sample_df = df.head(sample_size)
        
        return {
            "columns": df.columns.tolist(),
            "data": sample_df.to_dict('records'),
            "total_rows": len(df),
            "sample_size": len(sample_df)
        }
    
    def calculate_data_quality_score(self, df: pd.DataFrame) -> int:
        """Calculate a data quality score (0-100)."""
        total_cells = df.shape[0] * df.shape[1]
        missing_cells = df.isnull().sum().sum()
        duplicate_rows = df.duplicated().sum()
        
        # Calculate completeness score (0-50 points)
        completeness_score = max(0, 50 - (missing_cells / total_cells * 50))
        
        # Calculate uniqueness score (0-30 points)
        uniqueness_score = max(0, 30 - (duplicate_rows / df.shape[0] * 30))
        
        # Calculate consistency score (0-20 points) - simplified
        consistency_score = 20  # Placeholder for more complex consistency checks
        
        return int(completeness_score + uniqueness_score + consistency_score)
    
    def clean_dataset(
        self, 
        df: pd.DataFrame, 
        options: DataCleaningOptions
    ) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Clean dataset based on provided options."""
        original_shape = df.shape
        changes_summary = {}
        
        # Remove duplicates
        if options.remove_duplicates:
            duplicates_before = df.duplicated().sum()
            df = df.drop_duplicates()
            changes_summary["duplicates_removed"] = duplicates_before - df.duplicated().sum()
        
        # Handle missing values
        if options.handle_missing_values != "none":
            missing_before = df.isnull().sum().sum()
            
            if options.handle_missing_values == "drop":
                df = df.dropna()
            elif options.handle_missing_values in ["fill_mean", "fill_median", "fill_mode"]:
                for column in df.columns:
                    if df[column].isnull().any():
                        if df[column].dtype in ['int64', 'float64']:
                            if options.handle_missing_values == "fill_mean":
                                df[column].fillna(df[column].mean(), inplace=True)
                            elif options.handle_missing_values == "fill_median":
                                df[column].fillna(df[column].median(), inplace=True)
                        else:
                            # For categorical data, use mode
                            mode_value = df[column].mode()
                            if not mode_value.empty:
                                df[column].fillna(mode_value.iloc[0], inplace=True)
            elif options.fill_value:
                df = df.fillna(options.fill_value)
            
            missing_after = df.isnull().sum().sum()
            changes_summary["missing_values_handled"] = missing_before - missing_after
        
        # Remove outliers
        if options.remove_outliers:
            outliers_removed = 0
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            
            for column in numeric_columns:
                if options.outlier_method == "iqr":
                    Q1 = df[column].quantile(0.25)
                    Q3 = df[column].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    outliers_mask = (df[column] < lower_bound) | (df[column] > upper_bound)
                    outliers_removed += outliers_mask.sum()
                    df = df[~outliers_mask]
                
                elif options.outlier_method == "zscore":
                    z_scores = np.abs((df[column] - df[column].mean()) / df[column].std())
                    outliers_mask = z_scores > 3
                    outliers_removed += outliers_mask.sum()
                    df = df[~outliers_mask]
            
            changes_summary["outliers_removed"] = outliers_removed
        
        # Normalize columns
        if options.normalize_columns:
            scaler = StandardScaler()
            for column in options.normalize_columns:
                if column in df.columns and df[column].dtype in ['int64', 'float64']:
                    df[column] = scaler.fit_transform(df[[column]])
            changes_summary["normalized_columns"] = options.normalize_columns
        
        # Encode categorical variables
        if options.encode_categorical:
            encoded_columns = []
            for column in options.encode_categorical:
                if column in df.columns:
                    if options.encoding_method == "onehot":
                        # One-hot encoding
                        dummies = pd.get_dummies(df[column], prefix=column)
                        df = pd.concat([df, dummies], axis=1)
                        df = df.drop(column, axis=1)
                        encoded_columns.append(column)
                    elif options.encoding_method == "label":
                        # Label encoding
                        le = LabelEncoder()
                        df[column] = le.fit_transform(df[column].astype(str))
                        encoded_columns.append(column)
            
            changes_summary["encoded_columns"] = encoded_columns
        
        changes_summary.update({
            "original_shape": original_shape,
            "final_shape": df.shape,
            "rows_changed": original_shape[0] - df.shape[0],
            "columns_changed": df.shape[1] - original_shape[1]
        })
        
        return df, changes_summary
    
    def save_dataset(self, df: pd.DataFrame, file_path: str) -> None:
        """Save dataset to file."""
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        if extension == '.csv':
            df.to_csv(file_path, index=False)
        elif extension in ['.xlsx', '.xls']:
            df.to_excel(file_path, index=False)
        elif extension == '.json':
            df.to_json(file_path, orient='records')
        else:
            raise ValueError(f"Unsupported file format: {extension}")


# Global data processor instance
data_processor = DataProcessor()
