"""
ML Model management endpoints.
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.model import M<PERSON>ode<PERSON>, ModelPrediction
from app.models.dataset import Dataset
from app.schemas.model import (
    MLModel as MLModelSchema,
    MLModelCreate,
    MLModelUpdate,
    ModelTrainingRequest,
    ModelPredictionRequest,
    ModelPredictionResponse
)

router = APIRouter()


@router.post("/", response_model=MLModelSchema)
def create_model(
    model_data: MLModelCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Create a new ML model."""
    # Verify dataset ownership
    dataset = db.query(Dataset).filter(
        Dataset.id == model_data.dataset_id,
        Dataset.owner_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    model = MLModel(
        name=model_data.name,
        description=model_data.description,
        model_type=model_data.model_type,
        algorithm=model_data.algorithm,
        dataset_id=model_data.dataset_id,
        target_column=model_data.target_column,
        feature_columns=model_data.feature_columns,
        hyperparameters=model_data.hyperparameters,
        owner_id=current_user.id,
        model_file_path="",  # Will be set after training
        status="created"
    )
    
    db.add(model)
    db.commit()
    db.refresh(model)
    
    return model


@router.get("/", response_model=List[MLModelSchema])
def get_models(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get user's ML models."""
    models = db.query(MLModel).filter(
        MLModel.owner_id == current_user.id
    ).order_by(MLModel.created_at.desc()).all()
    
    return models


@router.get("/{model_id}", response_model=MLModelSchema)
def get_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get a specific ML model."""
    model = db.query(MLModel).filter(
        MLModel.id == model_id,
        MLModel.owner_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    return model


@router.put("/{model_id}", response_model=MLModelSchema)
def update_model(
    model_id: int,
    model_update: MLModelUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Update ML model metadata."""
    model = db.query(MLModel).filter(
        MLModel.id == model_id,
        MLModel.owner_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    # Update fields
    if model_update.name is not None:
        model.name = model_update.name
    if model_update.description is not None:
        model.description = model_update.description
    
    db.commit()
    db.refresh(model)
    
    return model


@router.delete("/{model_id}")
def delete_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Delete an ML model."""
    model = db.query(MLModel).filter(
        MLModel.id == model_id,
        MLModel.owner_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    # Delete model file if exists
    if model.model_file_path:
        from app.services.file_manager import file_manager
        file_manager.delete_file(model.model_file_path)
    
    # Delete database record
    db.delete(model)
    db.commit()
    
    return {"message": "Model deleted successfully"}


@router.post("/{model_id}/train")
def train_model(
    model_id: int,
    training_request: ModelTrainingRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Start model training."""
    model = db.query(MLModel).filter(
        MLModel.id == model_id,
        MLModel.owner_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    if model.status == "training":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Model is already training"
        )
    
    # Update model status
    model.status = "training"
    if training_request.hyperparameters:
        model.hyperparameters = training_request.hyperparameters
    
    db.commit()
    
    # Queue training task
    from app.tasks.model_tasks import train_model_task
    train_model_task.delay(model_id, training_request.dict())
    
    return {
        "message": "Model training started",
        "model_id": model_id,
        "status": "training"
    }


@router.post("/{model_id}/predict", response_model=ModelPredictionResponse)
def predict(
    model_id: int,
    prediction_request: ModelPredictionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Make predictions with a trained model."""
    model = db.query(MLModel).filter(
        MLModel.id == model_id,
        MLModel.owner_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    if model.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Model is not trained yet"
        )
    
    try:
        # Load model and make prediction
        from app.services.ml_service import ml_service
        prediction_result = ml_service.predict(
            model.model_file_path,
            prediction_request.input_data
        )
        
        # Save prediction record
        prediction = ModelPrediction(
            model_id=model_id,
            input_data=prediction_request.input_data,
            prediction=prediction_result["prediction"],
            confidence_score=prediction_result.get("confidence")
        )
        
        db.add(prediction)
        db.commit()
        db.refresh(prediction)
        
        return ModelPredictionResponse(
            prediction_id=prediction.id,
            prediction=prediction_result["prediction"],
            confidence_score=prediction_result.get("confidence"),
            model_id=model_id
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Prediction failed: {str(e)}"
        )


@router.get("/{model_id}/predictions")
def get_model_predictions(
    model_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get predictions made by a model."""
    model = db.query(MLModel).filter(
        MLModel.id == model_id,
        MLModel.owner_id == current_user.id
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    predictions = db.query(ModelPrediction).filter(
        ModelPrediction.model_id == model_id
    ).order_by(ModelPrediction.prediction_time.desc()).all()
    
    return predictions
