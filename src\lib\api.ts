/**
 * API client for Diorite backend integration
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    
    // Load token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('diorite_token');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  // Authentication methods
  async login(username: string, password: string): Promise<ApiResponse<{ access_token: string; refresh_token: string }>> {
    const response = await this.request<{ access_token: string; refresh_token: string }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });

    if (response.success && response.data) {
      this.token = response.data.access_token;
      if (typeof window !== 'undefined') {
        localStorage.setItem('diorite_token', response.data.access_token);
        localStorage.setItem('diorite_refresh_token', response.data.refresh_token);
      }
    }

    return response;
  }

  async register(userData: {
    email: string;
    username: string;
    password: string;
    full_name?: string;
  }): Promise<ApiResponse<any>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getCurrentUser(): Promise<ApiResponse<any>> {
    return this.request('/auth/me');
  }

  logout(): void {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('diorite_token');
      localStorage.removeItem('diorite_refresh_token');
    }
  }

  // Dataset methods
  async uploadDataset(file: File, name?: string, description?: string): Promise<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);
    if (name) formData.append('name', name);
    if (description) formData.append('description', description);

    const headers: HeadersInit = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(`${this.baseURL}/datasets/upload`, {
        method: 'POST',
        headers,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.detail || `Upload failed: ${response.statusText}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  async getDatasets(): Promise<ApiResponse<any>> {
    return this.request('/datasets/');
  }

  async getDataset(id: number): Promise<ApiResponse<any>> {
    return this.request(`/datasets/${id}`);
  }

  async getDatasetPreview(id: number, sampleSize: number = 100): Promise<ApiResponse<any>> {
    return this.request(`/datasets/${id}/preview?sample_size=${sampleSize}`);
  }

  async getDatasetStats(id: number): Promise<ApiResponse<any>> {
    return this.request(`/datasets/${id}/stats`);
  }

  // Chat methods
  async createChatSession(title?: string, contextData?: any): Promise<ApiResponse<any>> {
    return this.request('/chat/sessions', {
      method: 'POST',
      body: JSON.stringify({ title, context_data: contextData }),
    });
  }

  async getChatSessions(): Promise<ApiResponse<any>> {
    return this.request('/chat/sessions');
  }

  async getChatMessages(sessionId: number): Promise<ApiResponse<any>> {
    return this.request(`/chat/sessions/${sessionId}/messages`);
  }

  async sendMessage(sessionId: number, content: string): Promise<ApiResponse<any>> {
    return this.request(`/chat/sessions/${sessionId}/messages`, {
      method: 'POST',
      body: JSON.stringify({ content }),
    });
  }

  // ML Model methods
  async getModels(): Promise<ApiResponse<any>> {
    return this.request('/models/');
  }

  async createModel(modelData: {
    name: string;
    description?: string;
    model_type: string;
    algorithm: string;
    dataset_id: number;
    target_column?: string;
    feature_columns?: string[];
  }): Promise<ApiResponse<any>> {
    return this.request('/models/', {
      method: 'POST',
      body: JSON.stringify(modelData),
    });
  }

  async trainModel(modelId: number, hyperparameters?: any): Promise<ApiResponse<any>> {
    return this.request(`/models/${modelId}/train`, {
      method: 'POST',
      body: JSON.stringify({ hyperparameters }),
    });
  }

  async predict(modelId: number, inputData: any): Promise<ApiResponse<any>> {
    return this.request(`/models/${modelId}/predict`, {
      method: 'POST',
      body: JSON.stringify({ input_data: inputData }),
    });
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    return this.request('/health', { method: 'GET' });
  }

  // Set token manually (for testing or external auth)
  setToken(token: string): void {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('diorite_token', token);
    }
  }

  // Get current token
  getToken(): string | null {
    return this.token;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export types for use in components
export type { ApiResponse };
