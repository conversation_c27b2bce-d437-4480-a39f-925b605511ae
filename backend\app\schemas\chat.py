"""
Pydantic schemas for chat-related operations.
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel


class ChatSessionBase(BaseModel):
    """Base chat session schema."""
    title: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None


class ChatSessionCreate(ChatSessionBase):
    """Schema for chat session creation."""
    pass


class ChatSessionUpdate(BaseModel):
    """Schema for chat session updates."""
    title: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None


class ChatSession(ChatSessionBase):
    """Chat session schema."""
    id: int
    user_id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class ChatMessageBase(BaseModel):
    """Base chat message schema."""
    content: str
    message_type: str  # user, assistant, system, tool


class ChatMessageCreate(BaseModel):
    """Schema for chat message creation."""
    content: str


class ChatMessage(ChatMessageBase):
    """Chat message schema."""
    id: int
    session_id: int
    metadata: Optional[Dict[str, Any]] = None
    tool_calls: Optional[Dict[str, Any]] = None
    tool_results: Optional[Dict[str, Any]] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class ChatResponse(BaseModel):
    """Chat response schema."""
    user_message: ChatMessage
    agent_message: ChatMessage
    success: bool


class AgentToolBase(BaseModel):
    """Base agent tool schema."""
    name: str
    description: str
    function_name: str
    parameters_schema: Dict[str, Any]
    category: str
    requires_dataset: bool = False


class AgentToolCreate(AgentToolBase):
    """Schema for agent tool creation."""
    pass


class AgentTool(AgentToolBase):
    """Agent tool schema."""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class ChatSessionWithMessages(ChatSession):
    """Chat session with messages."""
    messages: List[ChatMessage] = []
