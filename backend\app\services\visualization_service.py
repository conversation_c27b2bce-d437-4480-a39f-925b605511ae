"""
Visualization service for creating charts and plots.
"""
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any, Optional
import base64
import io
import json

class VisualizationService:
    """Service for creating data visualizations."""
    
    def __init__(self):
        # Set style for matplotlib/seaborn
        plt.style.use('default')
        sns.set_palette("husl")
    
    def create_chart(
        self, 
        df: pd.DataFrame, 
        chart_type: str, 
        columns: List[str],
        **kwargs
    ) -> Dict[str, Any]:
        """Create a chart based on type and columns."""
        
        chart_functions = {
            "histogram": self._create_histogram,
            "scatter": self._create_scatter,
            "line": self._create_line_chart,
            "bar": self._create_bar_chart,
            "box": self._create_box_plot,
            "heatmap": self._create_heatmap,
            "correlation": self._create_correlation_matrix,
            "distribution": self._create_distribution_plot,
            "pie": self._create_pie_chart
        }
        
        if chart_type not in chart_functions:
            raise ValueError(f"Unsupported chart type: {chart_type}")
        
        return chart_functions[chart_type](df, columns, **kwargs)
    
    def _create_histogram(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create histogram."""
        if not columns:
            # Auto-select numeric columns
            columns = df.select_dtypes(include=['number']).columns.tolist()[:1]
        
        if not columns:
            raise ValueError("No numeric columns found for histogram")
        
        column = columns[0]
        
        # Create plotly histogram
        fig = px.histogram(
            df, 
            x=column,
            title=f"Distribution of {column}",
            nbins=kwargs.get('bins', 30)
        )
        
        return {
            "chart_type": "histogram",
            "data": fig.to_json(),
            "column": column,
            "stats": {
                "mean": df[column].mean(),
                "std": df[column].std(),
                "min": df[column].min(),
                "max": df[column].max()
            }
        }
    
    def _create_scatter(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create scatter plot."""
        if len(columns) < 2:
            # Auto-select numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            if len(numeric_cols) < 2:
                raise ValueError("Need at least 2 numeric columns for scatter plot")
            columns = numeric_cols[:2]
        
        x_col, y_col = columns[0], columns[1]
        color_col = columns[2] if len(columns) > 2 else None
        
        fig = px.scatter(
            df,
            x=x_col,
            y=y_col,
            color=color_col,
            title=f"{y_col} vs {x_col}",
            hover_data=columns[:3]
        )
        
        # Calculate correlation
        correlation = df[x_col].corr(df[y_col]) if df[x_col].dtype in ['int64', 'float64'] and df[y_col].dtype in ['int64', 'float64'] else None
        
        return {
            "chart_type": "scatter",
            "data": fig.to_json(),
            "x_column": x_col,
            "y_column": y_col,
            "color_column": color_col,
            "correlation": correlation
        }
    
    def _create_line_chart(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create line chart."""
        if not columns:
            # Auto-select first numeric column
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            if not numeric_cols:
                raise ValueError("No numeric columns found for line chart")
            columns = [numeric_cols[0]]
        
        y_col = columns[0]
        x_col = kwargs.get('x_column', df.index.name or 'Index')
        
        if x_col == 'Index':
            df_plot = df.reset_index()
            x_data = df_plot.index
        else:
            x_data = df[x_col]
        
        fig = px.line(
            x=x_data,
            y=df[y_col],
            title=f"{y_col} over {x_col}",
            labels={'x': x_col, 'y': y_col}
        )
        
        return {
            "chart_type": "line",
            "data": fig.to_json(),
            "x_column": x_col,
            "y_column": y_col
        }
    
    def _create_bar_chart(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create bar chart."""
        if not columns:
            # Auto-select categorical and numeric columns
            cat_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            num_cols = df.select_dtypes(include=['number']).columns.tolist()
            if not cat_cols or not num_cols:
                raise ValueError("Need categorical and numeric columns for bar chart")
            columns = [cat_cols[0], num_cols[0]]
        
        if len(columns) == 1:
            # Value counts for single column
            value_counts = df[columns[0]].value_counts().head(10)
            fig = px.bar(
                x=value_counts.index,
                y=value_counts.values,
                title=f"Count of {columns[0]}",
                labels={'x': columns[0], 'y': 'Count'}
            )
            return {
                "chart_type": "bar",
                "data": fig.to_json(),
                "column": columns[0],
                "aggregation": "count"
            }
        else:
            # Grouped bar chart
            x_col, y_col = columns[0], columns[1]
            grouped = df.groupby(x_col)[y_col].mean().head(10)
            
            fig = px.bar(
                x=grouped.index,
                y=grouped.values,
                title=f"Average {y_col} by {x_col}",
                labels={'x': x_col, 'y': f'Average {y_col}'}
            )
            
            return {
                "chart_type": "bar",
                "data": fig.to_json(),
                "x_column": x_col,
                "y_column": y_col,
                "aggregation": "mean"
            }
    
    def _create_box_plot(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create box plot."""
        if not columns:
            # Auto-select numeric columns
            columns = df.select_dtypes(include=['number']).columns.tolist()[:1]
        
        if not columns:
            raise ValueError("No numeric columns found for box plot")
        
        y_col = columns[0]
        x_col = columns[1] if len(columns) > 1 else None
        
        if x_col:
            fig = px.box(df, x=x_col, y=y_col, title=f"Box Plot of {y_col} by {x_col}")
        else:
            fig = px.box(df, y=y_col, title=f"Box Plot of {y_col}")
        
        return {
            "chart_type": "box",
            "data": fig.to_json(),
            "y_column": y_col,
            "x_column": x_col
        }
    
    def _create_heatmap(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create heatmap."""
        if not columns:
            # Use all numeric columns
            columns = df.select_dtypes(include=['number']).columns.tolist()
        
        if len(columns) < 2:
            raise ValueError("Need at least 2 numeric columns for heatmap")
        
        # Create correlation matrix
        corr_matrix = df[columns].corr()
        
        fig = px.imshow(
            corr_matrix,
            title="Correlation Heatmap",
            color_continuous_scale="RdBu",
            aspect="auto"
        )
        
        return {
            "chart_type": "heatmap",
            "data": fig.to_json(),
            "columns": columns,
            "correlation_matrix": corr_matrix.to_dict()
        }
    
    def _create_correlation_matrix(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create correlation matrix visualization."""
        return self._create_heatmap(df, columns, **kwargs)
    
    def _create_distribution_plot(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create distribution plot with multiple columns."""
        if not columns:
            columns = df.select_dtypes(include=['number']).columns.tolist()[:3]
        
        if not columns:
            raise ValueError("No numeric columns found for distribution plot")
        
        # Create subplot with distributions
        fig = go.Figure()
        
        for col in columns:
            fig.add_trace(go.Histogram(
                x=df[col],
                name=col,
                opacity=0.7,
                nbinsx=30
            ))
        
        fig.update_layout(
            title="Distribution Comparison",
            xaxis_title="Value",
            yaxis_title="Frequency",
            barmode='overlay'
        )
        
        return {
            "chart_type": "distribution",
            "data": fig.to_json(),
            "columns": columns
        }
    
    def _create_pie_chart(self, df: pd.DataFrame, columns: List[str], **kwargs) -> Dict[str, Any]:
        """Create pie chart."""
        if not columns:
            # Auto-select categorical column
            cat_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            if not cat_cols:
                raise ValueError("No categorical columns found for pie chart")
            columns = [cat_cols[0]]
        
        column = columns[0]
        value_counts = df[column].value_counts().head(10)
        
        fig = px.pie(
            values=value_counts.values,
            names=value_counts.index,
            title=f"Distribution of {column}"
        )
        
        return {
            "chart_type": "pie",
            "data": fig.to_json(),
            "column": column,
            "categories": value_counts.to_dict()
        }


# Global visualization service instance
visualization_service = VisualizationService()
