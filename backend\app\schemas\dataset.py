"""
Pydantic schemas for dataset-related operations.
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, validator


class DatasetBase(BaseModel):
    """Base dataset schema."""
    name: str
    description: Optional[str] = None


class DatasetCreate(DatasetBase):
    """Schema for dataset creation."""
    pass


class DatasetUpdate(BaseModel):
    """Schema for dataset updates."""
    name: Optional[str] = None
    description: Optional[str] = None


class DatasetInfo(BaseModel):
    """Dataset information schema."""
    id: int
    name: str
    description: Optional[str]
    original_filename: str
    file_size: int
    file_type: str
    rows_count: Optional[int]
    columns_count: Optional[int]
    columns_info: Optional[Dict[str, Any]]
    is_processed: bool
    processing_status: str
    processing_error: Optional[str]
    missing_values_count: Optional[int]
    duplicate_rows_count: Optional[int]
    data_quality_score: Optional[int]
    owner_id: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class Dataset(DatasetInfo):
    """Public dataset schema."""
    pass


class DatasetList(BaseModel):
    """Dataset list response schema."""
    datasets: List[Dataset]
    total: int
    page: int
    size: int


class DatasetUploadResponse(BaseModel):
    """Dataset upload response schema."""
    dataset_id: int
    message: str
    processing_status: str


class DatasetPreview(BaseModel):
    """Dataset preview schema."""
    columns: List[str]
    data: List[Dict[str, Any]]
    total_rows: int
    sample_size: int


class DatasetStats(BaseModel):
    """Dataset statistics schema."""
    basic_stats: Dict[str, Any]
    column_stats: Dict[str, Dict[str, Any]]
    missing_values: Dict[str, int]
    data_types: Dict[str, str]
    correlations: Optional[Dict[str, Dict[str, float]]]


class DataCleaningOptions(BaseModel):
    """Data cleaning options schema."""
    remove_duplicates: bool = False
    handle_missing_values: str = "none"  # none, drop, fill_mean, fill_median, fill_mode
    fill_value: Optional[str] = None
    remove_outliers: bool = False
    outlier_method: str = "iqr"  # iqr, zscore
    normalize_columns: List[str] = []
    encode_categorical: List[str] = []
    encoding_method: str = "onehot"  # onehot, label


class DataCleaningResult(BaseModel):
    """Data cleaning result schema."""
    new_dataset_id: int
    changes_summary: Dict[str, Any]
    rows_before: int
    rows_after: int
    columns_before: int
    columns_after: int


# Dataset Version schemas
class DatasetVersionBase(BaseModel):
    """Base dataset version schema."""
    description: Optional[str] = None
    changes_description: Optional[str] = None


class DatasetVersionCreate(DatasetVersionBase):
    """Schema for dataset version creation."""
    dataset_id: int


class DatasetVersion(DatasetVersionBase):
    """Dataset version schema."""
    id: int
    dataset_id: int
    version_number: int
    file_size: int
    transformation_steps: Optional[Dict[str, Any]]
    created_at: datetime
    
    class Config:
        from_attributes = True
