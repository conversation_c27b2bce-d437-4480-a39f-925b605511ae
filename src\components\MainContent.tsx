'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import HistoryPanel from './HistoryPanel';

interface MainContentProps {
  userName?: string;
}

interface Dataset {
  id: number;
  name: string;
  original_filename: string;
  file_size: number;
  rows_count?: number;
  columns_count?: number;
  processing_status: string;
  created_at: string;
}

const MainContent: React.FC<MainContentProps> = ({ userName = "Ram" }) => {
  const { user, isAuthenticated } = useAuth();
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load datasets on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadDatasets();
    }
  }, [isAuthenticated]);

  const loadDatasets = async () => {
    try {
      const response = await apiClient.getDatasets();
      if (response.success && response.data) {
        setDatasets(response.data.datasets || []);
      }
    } catch (error) {
      console.error('Error loading datasets:', error);
    }
  };

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];

    // Validate file type
    const allowedTypes = ['.csv', '.xlsx', '.xls', '.json'];
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
      alert('Please upload a CSV, Excel, or JSON file.');
      return;
    }

    // Validate file size (100MB limit)
    if (file.size > 100 * 1024 * 1024) {
      alert('File size must be less than 100MB.');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const response = await apiClient.uploadDataset(file, file.name);

      if (response.success) {
        // Reload datasets to show the new upload
        await loadDatasets();
        alert('Dataset uploaded successfully!');
      } else {
        alert(`Upload failed: ${response.error}`);
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileUpload(e.target.files);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const displayUserName = user?.full_name || user?.username || userName;

  return (
    <div className="ml-20 min-h-screen bg-background flex flex-col">
      {/* Top Bar */}
      <div className="flex justify-between items-center p-8">
        {/* Brand Name */}
        <h1 className="text-text-dark font-bold text-4xl tracking-wide">
          DIORITE
        </h1>

        {/* User Avatar */}
        <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
          <span className="text-white font-semibold text-lg">
            {displayUserName.charAt(0).toUpperCase()}
          </span>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex items-center justify-between px-8 pb-8">
        {/* Welcome Message & CTA */}
        <div className="flex-1 max-w-2xl">
          {/* Greeting */}
          <h2 className="text-primary font-semibold text-5xl mb-4">
            Hello {displayUserName},
          </h2>

          {/* Instructional Text */}
          <p className="text-text-dark text-xl">
            Upload Dataset to{' '}
            <span className="text-primary font-semibold">Start</span>
          </p>

          {/* Upload Area */}
          <div className="mt-12">
            <div
              className={`border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-200 cursor-pointer ${
                dragActive
                  ? 'border-primary border-opacity-70 bg-primary bg-opacity-5'
                  : 'border-primary border-opacity-30 hover:border-opacity-50'
              } ${isUploading ? 'pointer-events-none opacity-50' : ''}`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              onClick={openFileDialog}
            >
              {isUploading ? (
                <div>
                  <div className="text-primary text-6xl mb-4">⏳</div>
                  <p className="text-text-dark text-lg mb-2">Uploading...</p>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-text-dark text-sm opacity-60">
                    Please wait while we process your file
                  </p>
                </div>
              ) : (
                <div>
                  <div className="text-primary text-6xl mb-4">📁</div>
                  <p className="text-text-dark text-lg mb-2">
                    Drag and drop your dataset here
                  </p>
                  <p className="text-text-dark text-sm opacity-60">
                    or click to browse files (CSV, Excel, JSON)
                  </p>
                </div>
              )}
            </div>

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv,.xlsx,.xls,.json"
              onChange={handleFileInputChange}
              className="hidden"
            />
          </div>

          {/* Recent uploads info */}
          {datasets.length > 0 && (
            <div className="mt-8">
              <h3 className="text-text-dark font-medium mb-3">Recent Uploads</h3>
              <div className="space-y-2">
                {datasets.slice(0, 3).map((dataset) => (
                  <div key={dataset.id} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                    <div>
                      <p className="font-medium text-text-dark">{dataset.name}</p>
                      <p className="text-sm text-gray-500">
                        {dataset.rows_count ? `${dataset.rows_count} rows, ${dataset.columns_count} columns` : 'Processing...'}
                      </p>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      dataset.processing_status === 'processed'
                        ? 'bg-green-100 text-green-800'
                        : dataset.processing_status === 'processing'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}>
                      {dataset.processing_status}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* History Panel */}
        <div className="ml-8">
          <HistoryPanel datasets={datasets} />
        </div>
      </div>
    </div>
  );
};

export default MainContent;
