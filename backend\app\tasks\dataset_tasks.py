"""
Celery tasks for dataset processing.
"""
from celery import current_task
from sqlalchemy.orm import Session

from app.tasks.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.dataset import Dataset
from app.services.data_processor import data_processor
import structlog

logger = structlog.get_logger()


@celery_app.task(bind=True)
def process_dataset_task(self, dataset_id: int):
    """Process uploaded dataset to extract metadata and statistics."""
    db: Session = SessionLocal()
    
    try:
        # Update task status
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "status": "Starting dataset processing"}
        )
        
        # Get dataset
        dataset = db.query(Dataset).filter(Dataset.id == dataset_id).first()
        if not dataset:
            raise Exception(f"Dataset {dataset_id} not found")
        
        # Update processing status
        dataset.processing_status = "processing"
        db.commit()
        
        logger.info("Starting dataset processing", dataset_id=dataset_id)
        
        # Load dataset
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 20, "total": 100, "status": "Loading dataset"}
        )
        
        df = data_processor.load_dataset(dataset.file_path)
        
        # Extract basic information
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 40, "total": 100, "status": "Extracting metadata"}
        )
        
        dataset_info = data_processor.get_dataset_info(df)
        
        # Update dataset metadata
        dataset.rows_count = dataset_info["shape"][0]
        dataset.columns_count = dataset_info["shape"][1]
        dataset.columns_info = {
            "columns": dataset_info["columns"],
            "dtypes": dataset_info["dtypes"]
        }
        dataset.missing_values_count = sum(dataset_info["missing_values"].values())
        dataset.duplicate_rows_count = dataset_info["duplicate_rows"]
        
        # Calculate data quality score
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 80, "total": 100, "status": "Calculating quality score"}
        )
        
        dataset.data_quality_score = data_processor.calculate_data_quality_score(df)
        
        # Mark as processed
        dataset.is_processed = True
        dataset.processing_status = "processed"
        dataset.processing_error = None
        
        db.commit()
        
        logger.info("Dataset processing completed", dataset_id=dataset_id)
        
        return {
            "status": "completed",
            "dataset_id": dataset_id,
            "rows": dataset.rows_count,
            "columns": dataset.columns_count,
            "quality_score": dataset.data_quality_score
        }
        
    except Exception as e:
        logger.error("Dataset processing failed", dataset_id=dataset_id, error=str(e))
        
        # Update error status
        if dataset:
            dataset.processing_status = "error"
            dataset.processing_error = str(e)
            db.commit()
        
        raise
    
    finally:
        db.close()


@celery_app.task(bind=True)
def clean_dataset_task(self, dataset_id: int, cleaning_options: dict, user_id: int):
    """Clean dataset based on provided options."""
    db: Session = SessionLocal()
    
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 0, "total": 100, "status": "Starting data cleaning"}
        )
        
        # Get original dataset
        original_dataset = db.query(Dataset).filter(
            Dataset.id == dataset_id,
            Dataset.owner_id == user_id
        ).first()
        
        if not original_dataset:
            raise Exception(f"Dataset {dataset_id} not found")
        
        logger.info("Starting data cleaning", dataset_id=dataset_id)
        
        # Load original dataset
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 20, "total": 100, "status": "Loading original dataset"}
        )
        
        df = data_processor.load_dataset(original_dataset.file_path)
        
        # Apply cleaning options
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 40, "total": 100, "status": "Applying cleaning operations"}
        )
        
        from app.schemas.dataset import DataCleaningOptions
        options = DataCleaningOptions(**cleaning_options)
        cleaned_df, changes_summary = data_processor.clean_dataset(df, options)
        
        # Save cleaned dataset
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 70, "total": 100, "status": "Saving cleaned dataset"}
        )
        
        from app.services.file_manager import file_manager
        cleaned_filename = f"cleaned_{original_dataset.original_filename}"
        cleaned_file_path, cleaned_file_size = file_manager.save_uploaded_file(
            None, cleaned_filename, user_id, "cleaned_datasets"
        )
        
        # Save the cleaned dataframe
        data_processor.save_dataset(cleaned_df, cleaned_file_path)
        
        # Create new dataset record
        cleaned_dataset = Dataset(
            name=f"Cleaned - {original_dataset.name}",
            description=f"Cleaned version of {original_dataset.name}",
            original_filename=cleaned_filename,
            file_path=cleaned_file_path,
            file_size=cleaned_file_size,
            file_type=original_dataset.file_type,
            owner_id=user_id,
            rows_count=cleaned_df.shape[0],
            columns_count=cleaned_df.shape[1],
            is_processed=True,
            processing_status="processed"
        )
        
        db.add(cleaned_dataset)
        db.commit()
        db.refresh(cleaned_dataset)
        
        logger.info("Data cleaning completed", 
                   original_dataset_id=dataset_id, 
                   cleaned_dataset_id=cleaned_dataset.id)
        
        return {
            "status": "completed",
            "original_dataset_id": dataset_id,
            "cleaned_dataset_id": cleaned_dataset.id,
            "changes_summary": changes_summary
        }
        
    except Exception as e:
        logger.error("Data cleaning failed", dataset_id=dataset_id, error=str(e))
        raise
    
    finally:
        db.close()
