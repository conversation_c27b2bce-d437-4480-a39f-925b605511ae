'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import ChatInterface from './ChatInterface';
import DataCleaningPanel from './DataCleaningPanel';
import ManualPlottingPanel from './ManualPlottingPanel';
import ModelBuilderPanel from './ModelBuilderPanel';

interface Dataset {
  id: number;
  name: string;
  original_filename: string;
  file_size: number;
  rows_count?: number;
  columns_count?: number;
  processing_status: string;
  created_at: string;
}

const DataScienceDashboard: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState<'chat' | 'cleaning' | 'plotting' | 'modeling' | 'analysis'>('chat');
  const [selectedDatasetId, setSelectedDatasetId] = useState<number | null>(null);
  const [datasets, setDatasets] = useState<Dataset[]>([]);

  useEffect(() => {
    if (isAuthenticated) {
      loadDatasets();
    }
  }, [isAuthenticated]);

  const loadDatasets = async () => {
    try {
      const response = await apiClient.getDatasets();
      if (response.success && response.data) {
        setDatasets(response.data.datasets || []);
        // Auto-select first dataset if none selected
        if (!selectedDatasetId && response.data.datasets?.length > 0) {
          setSelectedDatasetId(response.data.datasets[0].id);
        }
      }
    } catch (error) {
      console.error('Error loading datasets:', error);
    }
  };

  const tabs = [
    { id: 'chat', label: 'AI Assistant', icon: '🤖', description: 'Chat with AI for data science help' },
    { id: 'cleaning', label: 'Data Cleaning', icon: '🧹', description: 'Clean and preprocess your data' },
    { id: 'plotting', label: 'Visualization', icon: '📈', description: 'Create custom plots and charts' },
    { id: 'modeling', label: 'ML Models', icon: '🔬', description: 'Build and train machine learning models' },
    { id: 'analysis', label: 'Analysis', icon: '📊', description: 'Statistical analysis and insights' }
  ];

  const selectedDataset = datasets.find(d => d.id === selectedDatasetId);

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Data Science Workspace</h1>
            <p className="text-gray-600">Analyze, visualize, and model your data</p>
          </div>
          
          {/* Dataset Selector */}
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Dataset:</span>
            <select
              value={selectedDatasetId || ''}
              onChange={(e) => setSelectedDatasetId(e.target.value ? parseInt(e.target.value) : null)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-transparent min-w-48"
            >
              <option value="">Select a dataset...</option>
              {datasets.map(dataset => (
                <option key={dataset.id} value={dataset.id}>
                  {dataset.name} ({dataset.rows_count || 0} rows)
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200 px-6">
        <div className="flex space-x-8 overflow-x-auto">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`pb-4 pt-4 px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              title={tab.description}
            >
              <div className="flex items-center space-x-2">
                <span className="text-lg">{tab.icon}</span>
                <span>{tab.label}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Dataset Info Bar */}
      {selectedDataset && (
        <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div>
                <span className="text-sm font-medium text-blue-900">
                  {selectedDataset.name}
                </span>
                <span className="text-sm text-blue-700 ml-2">
                  ({selectedDataset.original_filename})
                </span>
              </div>
              <div className="flex items-center space-x-4 text-sm text-blue-700">
                <span>{selectedDataset.rows_count || 0} rows</span>
                <span>•</span>
                <span>{selectedDataset.columns_count || 0} columns</span>
                <span>•</span>
                <span>{(selectedDataset.file_size / 1024 / 1024).toFixed(2)} MB</span>
              </div>
            </div>
            <div className={`px-2 py-1 rounded text-xs font-medium ${
              selectedDataset.processing_status === 'processed'
                ? 'bg-green-100 text-green-800'
                : selectedDataset.processing_status === 'processing'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
            }`}>
              {selectedDataset.processing_status}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {activeTab === 'chat' && (
          <div className="h-full">
            <ChatInterface />
          </div>
        )}
        
        {activeTab === 'cleaning' && (
          <div className="p-6">
            {selectedDatasetId ? (
              <DataCleaningPanel 
                datasetId={selectedDatasetId}
                onCleaningComplete={(result) => {
                  console.log('Cleaning completed:', result);
                  // Refresh datasets after cleaning
                  loadDatasets();
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <div className="text-6xl mb-4">🧹</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Data Cleaning</h3>
                  <p className="text-gray-600 max-w-md">
                    Select a dataset to start cleaning your data. Remove duplicates, handle missing values, and filter outliers.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'plotting' && (
          <div className="p-6">
            {selectedDatasetId ? (
              <ManualPlottingPanel 
                datasetId={selectedDatasetId}
                onPlotCreated={(plot) => {
                  console.log('Plot created:', plot);
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <div className="text-6xl mb-4">📈</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Data Visualization</h3>
                  <p className="text-gray-600 max-w-md">
                    Select a dataset to create custom visualizations. Build histograms, scatter plots, bar charts, and more.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'modeling' && (
          <div className="p-6">
            {selectedDatasetId ? (
              <ModelBuilderPanel 
                datasetId={selectedDatasetId}
                onModelCreated={(model) => {
                  console.log('Model created:', model);
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <div className="text-6xl mb-4">🔬</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Machine Learning</h3>
                  <p className="text-gray-600 max-w-md">
                    Select a dataset to build and train machine learning models. Choose from classification, regression, or clustering algorithms.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'analysis' && (
          <div className="p-6">
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <div className="text-6xl mb-4">📊</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Statistical Analysis</h3>
                <p className="text-gray-600 max-w-md">
                  Coming soon! Perform correlation analysis, hypothesis testing, and generate statistical summaries.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataScienceDashboard;
