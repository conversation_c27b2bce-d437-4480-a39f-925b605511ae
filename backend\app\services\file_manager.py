"""
File management service for handling uploads and storage.
"""
import os
import shutil
from typing import Optional, BinaryIO
from pathlib import Path
import uuid
from datetime import datetime

from app.core.config import settings


class FileManager:
    """File management service."""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_unique_filename(self, original_filename: str) -> str:
        """Generate a unique filename while preserving extension."""
        file_path = Path(original_filename)
        extension = file_path.suffix
        unique_id = uuid.uuid4().hex
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}_{unique_id}{extension}"
    
    def get_user_directory(self, user_id: int) -> Path:
        """Get or create user-specific directory."""
        user_dir = self.upload_dir / f"user_{user_id}"
        user_dir.mkdir(parents=True, exist_ok=True)
        return user_dir
    
    def save_uploaded_file(
        self, 
        file_content: BinaryIO, 
        original_filename: str, 
        user_id: int,
        subdirectory: str = "datasets"
    ) -> tuple[str, int]:
        """
        Save uploaded file and return file path and size.
        
        Args:
            file_content: File content as binary stream
            original_filename: Original filename
            user_id: User ID for directory organization
            subdirectory: Subdirectory within user folder
            
        Returns:
            Tuple of (file_path, file_size)
        """
        # Create user and subdirectory
        user_dir = self.get_user_directory(user_id)
        target_dir = user_dir / subdirectory
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate unique filename
        unique_filename = self.generate_unique_filename(original_filename)
        file_path = target_dir / unique_filename
        
        # Save file
        file_size = 0
        with open(file_path, "wb") as buffer:
            while chunk := file_content.read(8192):  # Read in 8KB chunks
                buffer.write(chunk)
                file_size += len(chunk)
        
        return str(file_path), file_size
    
    def delete_file(self, file_path: str) -> bool:
        """Delete a file."""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False
    
    def copy_file(self, source_path: str, user_id: int, subdirectory: str = "datasets") -> str:
        """Copy a file to a new location."""
        source = Path(source_path)
        if not source.exists():
            raise FileNotFoundError(f"Source file not found: {source_path}")
        
        # Create target directory
        user_dir = self.get_user_directory(user_id)
        target_dir = user_dir / subdirectory
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate unique filename
        unique_filename = self.generate_unique_filename(source.name)
        target_path = target_dir / unique_filename
        
        # Copy file
        shutil.copy2(source, target_path)
        return str(target_path)
    
    def get_file_size(self, file_path: str) -> int:
        """Get file size in bytes."""
        try:
            return os.path.getsize(file_path)
        except OSError:
            return 0
    
    def file_exists(self, file_path: str) -> bool:
        """Check if file exists."""
        return os.path.exists(file_path)
    
    def validate_file_type(self, filename: str) -> bool:
        """Validate file type based on extension."""
        file_path = Path(filename)
        extension = file_path.suffix.lower()
        return extension in settings.ALLOWED_FILE_TYPES
    
    def validate_file_size(self, file_size: int) -> bool:
        """Validate file size."""
        max_size_bytes = settings.MAX_FILE_SIZE * 1024 * 1024  # Convert MB to bytes
        return file_size <= max_size_bytes
    
    def cleanup_user_files(self, user_id: int) -> bool:
        """Clean up all files for a user."""
        try:
            user_dir = self.get_user_directory(user_id)
            if user_dir.exists():
                shutil.rmtree(user_dir)
                return True
            return False
        except Exception:
            return False


# Global file manager instance
file_manager = FileManager()
