#!/bin/bash

# Diorite Backend Startup Script

set -e

echo "🚀 Starting Diorite Backend..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your configuration before running again."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "🐍 Creating Python virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start services with Docker Compose
echo "🐳 Starting services with Docker Compose..."
docker-compose up -d postgres redis minio

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Run database migrations
echo "🗄️  Running database migrations..."
alembic upgrade head

# Start the FastAPI server
echo "🌟 Starting FastAPI server..."
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

echo "✅ Diorite Backend is running!"
echo "📖 API Documentation: http://localhost:8000/api/v1/docs"
echo "🌸 Flower (Celery Monitor): http://localhost:5555"
echo "💾 MinIO Console: http://localhost:9001"
