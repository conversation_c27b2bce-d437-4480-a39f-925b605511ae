"""
Dataset management endpoints.
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.dataset import Dataset, DatasetVersion
from app.schemas.dataset import (
    Dataset as DatasetSchema,
    DatasetCreate,
    DatasetUpdate,
    DatasetList,
    DatasetUploadResponse,
    DatasetPreview,
    DatasetStats,
    DataCleaningOptions,
    DataCleaningResult
)
from app.services.file_manager import file_manager
from app.services.data_processor import data_processor
from app.tasks.dataset_tasks import process_dataset_task

router = APIRouter()


@router.post("/upload", response_model=DatasetUploadResponse)
async def upload_dataset(
    file: UploadFile = File(...),
    name: str = None,
    description: str = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Upload a new dataset."""
    # Validate file type
    if not file.filename or not file_manager.validate_file_type(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type. Supported formats: CSV, Excel, JSON"
        )
    
    # Read file content and validate size
    file_content = await file.read()
    if not file_manager.validate_file_size(len(file_content)):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File too large. Maximum size: {file_manager.settings.MAX_FILE_SIZE}MB"
        )
    
    # Save file
    from io import BytesIO
    file_stream = BytesIO(file_content)
    file_path, file_size = file_manager.save_uploaded_file(
        file_stream, file.filename, current_user.id
    )
    
    # Create dataset record
    dataset = Dataset(
        name=name or file.filename,
        description=description,
        original_filename=file.filename,
        file_path=file_path,
        file_size=file_size,
        file_type=file.filename.split('.')[-1].lower(),
        owner_id=current_user.id,
        processing_status="uploaded"
    )
    
    db.add(dataset)
    db.commit()
    db.refresh(dataset)
    
    # Queue dataset processing task
    process_dataset_task.delay(dataset.id)
    
    return DatasetUploadResponse(
        dataset_id=dataset.id,
        message="Dataset uploaded successfully",
        processing_status="processing"
    )


@router.get("/", response_model=DatasetList)
def get_datasets(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get user's datasets."""
    datasets = db.query(Dataset).filter(
        Dataset.owner_id == current_user.id
    ).offset(skip).limit(limit).all()
    
    total = db.query(Dataset).filter(
        Dataset.owner_id == current_user.id
    ).count()
    
    return DatasetList(
        datasets=datasets,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/{dataset_id}", response_model=DatasetSchema)
def get_dataset(
    dataset_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get a specific dataset."""
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.owner_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    return dataset


@router.put("/{dataset_id}", response_model=DatasetSchema)
def update_dataset(
    dataset_id: int,
    dataset_update: DatasetUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Update dataset metadata."""
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.owner_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    # Update fields
    if dataset_update.name is not None:
        dataset.name = dataset_update.name
    if dataset_update.description is not None:
        dataset.description = dataset_update.description
    
    db.commit()
    db.refresh(dataset)
    
    return dataset


@router.delete("/{dataset_id}")
def delete_dataset(
    dataset_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Delete a dataset."""
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.owner_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    # Delete file
    file_manager.delete_file(dataset.file_path)
    
    # Delete database record
    db.delete(dataset)
    db.commit()
    
    return {"message": "Dataset deleted successfully"}


@router.get("/{dataset_id}/preview", response_model=DatasetPreview)
def get_dataset_preview(
    dataset_id: int,
    sample_size: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get dataset preview."""
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.owner_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    if not dataset.is_processed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Dataset is still being processed"
        )
    
    # Load and preview dataset
    try:
        df = data_processor.load_dataset(dataset.file_path)
        preview = data_processor.get_data_preview(df, sample_size)
        return DatasetPreview(**preview)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error loading dataset: {str(e)}"
        )


@router.get("/{dataset_id}/stats", response_model=DatasetStats)
def get_dataset_stats(
    dataset_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get dataset statistics."""
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.owner_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )
    
    if not dataset.is_processed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Dataset is still being processed"
        )
    
    # Load dataset and calculate statistics
    try:
        df = data_processor.load_dataset(dataset.file_path)
        basic_stats = data_processor.get_dataset_info(df)
        column_stats = data_processor.get_column_statistics(df)
        
        # Calculate correlations for numeric columns
        numeric_df = df.select_dtypes(include=['number'])
        correlations = None
        if not numeric_df.empty:
            correlations = numeric_df.corr().to_dict()
        
        return DatasetStats(
            basic_stats=basic_stats,
            column_stats=column_stats,
            missing_values=basic_stats["missing_values"],
            data_types=basic_stats["dtypes"],
            correlations=correlations
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error calculating statistics: {str(e)}"
        )
