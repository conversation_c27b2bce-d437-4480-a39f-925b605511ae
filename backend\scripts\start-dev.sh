#!/bin/bash

# Diorite Development Environment Startup Script

set -e

echo "🚀 Starting Diorite Development Environment..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your configuration."
    echo "🔑 Don't forget to add your OPENAI_API_KEY!"
fi

# Start all services with Docker Compose
echo "🐳 Starting all services with Docker Compose..."
docker-compose up --build

echo "✅ Diorite Development Environment is running!"
echo ""
echo "🌐 Services:"
echo "  📖 API Documentation: http://localhost:8000/api/v1/docs"
echo "  🌸 Flower (Celery Monitor): http://localhost:5555"
echo "  💾 MinIO Console: http://localhost:9001 (admin/minioadmin)"
echo "  🗄️  PostgreSQL: localhost:5432"
echo "  🔴 Redis: localhost:6379"
echo ""
echo "🛑 To stop all services: docker-compose down"
