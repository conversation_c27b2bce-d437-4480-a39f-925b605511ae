'use client';

import React from 'react';
import Plot<PERSON>enderer from './PlotRenderer';
import ArtifactViewer, { Artifact } from './ArtifactViewer';

const DemoDataGenerator: React.FC = () => {
  const [showDemo, setShowDemo] = React.useState(false);

  // Sample plot data for demonstration
  const samplePlotData = {
    data: [
      {
        x: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        y: [20, 14, 23, 25, 22, 16],
        type: 'scatter',
        mode: 'lines+markers',
        marker: { color: '#E95B5B' },
        name: 'Sales'
      }
    ],
    layout: {
      title: 'Monthly Sales Data',
      xaxis: { title: 'Month' },
      yaxis: { title: 'Sales (thousands)' },
      showlegend: true
    }
  };

  const sampleArtifacts: Artifact[] = [
    {
      id: 'demo-plot-1',
      type: 'plot',
      title: 'Sales Trend Analysis',
      content: samplePlotData,
      metadata: {
        chart_type: 'line',
        columns: ['month', 'sales'],
        description: 'Monthly sales trend showing seasonal patterns'
      }
    },
    {
      id: 'demo-analysis-1',
      type: 'analysis',
      title: 'Data Summary',
      content: {
        total_records: 1000,
        missing_values: 23,
        data_quality_score: 87,
        key_insights: [
          'Sales show strong seasonal pattern',
          'Peak performance in April',
          'Slight decline in summer months'
        ]
      },
      metadata: {
        description: 'Statistical analysis of the dataset'
      }
    },
    {
      id: 'demo-table-1',
      type: 'table',
      title: 'Sample Data',
      content: {
        columns: ['Month', 'Sales', 'Growth'],
        data: [
          ['Jan', 20, '5%'],
          ['Feb', 14, '-30%'],
          ['Mar', 23, '64%'],
          ['Apr', 25, '9%'],
          ['May', 22, '-12%'],
          ['Jun', 16, '-27%']
        ]
      },
      metadata: {
        description: 'Sample of the processed data'
      }
    }
  ];

  if (!showDemo) {
    return (
      <div className="p-4">
        <button
          onClick={() => setShowDemo(true)}
          className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-colors"
        >
          Show Demo Artifacts
        </button>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="mb-4">
        <button
          onClick={() => setShowDemo(false)}
          className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-colors"
        >
          Hide Demo
        </button>
      </div>

      <div className="space-y-6">
        {/* Inline Plot Demo */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Inline Plot Example</h3>
          <PlotRenderer plotData={samplePlotData} />
        </div>

        {/* Artifact Viewer Demo */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Artifact Viewer Demo</h3>
          <p className="text-gray-600 mb-4">
            This demonstrates how artifacts appear in the chat interface with navigation.
          </p>
          <ArtifactViewer
            artifacts={sampleArtifacts}
            isOpen={true}
            onClose={() => {}}
          />
        </div>
      </div>
    </div>
  );
};

export default DemoDataGenerator;
