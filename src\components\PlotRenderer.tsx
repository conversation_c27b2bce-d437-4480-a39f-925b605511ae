'use client';

import React, { useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import Plotly to avoid SSR issues
const Plot = dynamic(() => import('react-plotly.js'), { 
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
});

interface PlotRendererProps {
  plotData: string | object;
  title?: string;
  className?: string;
}

const PlotRenderer: React.FC<PlotRendererProps> = ({ 
  plotData, 
  title,
  className = "w-full h-96"
}) => {
  const [plotConfig, setPlotConfig] = React.useState<any>(null);
  const [error, setError] = React.useState<string | null>(null);

  useEffect(() => {
    try {
      let parsedData;
      
      if (typeof plotData === 'string') {
        parsedData = JSON.parse(plotData);
      } else {
        parsedData = plotData;
      }

      // Ensure the data has the required structure
      if (parsedData && (parsedData.data || parsedData.layout)) {
        setPlotConfig({
          data: parsedData.data || [],
          layout: {
            ...parsedData.layout,
            title: title || parsedData.layout?.title,
            autosize: true,
            margin: { l: 50, r: 50, t: 50, b: 50 },
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: {
              family: 'Inter, system-ui, sans-serif',
              size: 12,
              color: '#4A4A4A'
            }
          },
          config: {
            responsive: true,
            displayModeBar: true,
            displaylogo: false,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            ...parsedData.config
          }
        });
        setError(null);
      } else {
        setError('Invalid plot data structure');
      }
    } catch (err) {
      console.error('Error parsing plot data:', err);
      setError('Failed to parse plot data');
    }
  }, [plotData, title]);

  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300`}>
        <div className="text-center">
          <div className="text-red-500 text-4xl mb-2">📊</div>
          <p className="text-gray-600 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!plotConfig) {
    return (
      <div className={`${className} flex items-center justify-center`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Plot
        data={plotConfig.data}
        layout={plotConfig.layout}
        config={plotConfig.config}
        style={{ width: '100%', height: '100%' }}
        useResizeHandler={true}
      />
    </div>
  );
};

export default PlotRenderer;
