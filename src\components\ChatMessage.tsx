'use client';

import React, { useState } from 'react';
import { Artifact } from './ArtifactViewer';

interface ChatMessageProps {
  message: {
    id: number;
    content: string;
    message_type: 'user' | 'assistant' | 'system';
    metadata?: {
      success?: boolean;
      intermediate_steps?: any[];
      artifacts?: Artifact[];
    };
    created_at: string;
  };
  onViewArtifacts?: (artifacts: Artifact[]) => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, onViewArtifacts }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const isUser = message.message_type === 'user';
  const isSystem = message.message_type === 'system';

  // Extract artifacts from message content or metadata
  const extractArtifacts = (): Artifact[] => {
    const artifacts: Artifact[] = [];

    // Check if metadata contains artifacts
    if (message.metadata?.artifacts) {
      artifacts.push(...message.metadata.artifacts);
    }

    // Parse content for embedded artifacts (JSON blocks and tool responses)
    try {
      // Look for JSON blocks in the content
      const jsonMatches = message.content.match(/```json\n([\s\S]*?)\n```/g);
      if (jsonMatches) {
        jsonMatches.forEach((match, index) => {
          const jsonContent = match.replace(/```json\n|\n```/g, '');
          try {
            const parsed = JSON.parse(jsonContent);

            // Check if this is an artifact response from the AI agent
            if (parsed.artifact) {
              artifacts.push({
                id: `${message.id}-artifact-${index}`,
                type: parsed.artifact.type || 'text',
                title: parsed.artifact.title || `Artifact ${index + 1}`,
                content: parsed.artifact.data || parsed.artifact.content || parsed.artifact,
                metadata: {
                  ...parsed.artifact.metadata,
                  timestamp: message.created_at,
                  description: parsed.message || `Generated from message ${message.id}`
                }
              });
            } else {
              // Determine artifact type based on content structure
              let type: Artifact['type'] = 'text';
              if (parsed.chart_type || (parsed.data && Array.isArray(parsed.data))) {
                type = 'plot';
              } else if (parsed.columns && parsed.data) {
                type = 'table';
              } else if (typeof parsed === 'object' && parsed.constructor === Object) {
                type = 'analysis';
              }

              artifacts.push({
                id: `${message.id}-artifact-${index}`,
                type,
                title: parsed.title || `${type.charAt(0).toUpperCase() + type.slice(1)} ${index + 1}`,
                content: parsed,
                metadata: {
                  timestamp: message.created_at,
                  description: `Generated from message ${message.id}`
                }
              });
            }
          } catch (e) {
            // If JSON parsing fails, treat as code
            artifacts.push({
              id: `${message.id}-code-${index}`,
              type: 'code',
              title: `Code Block ${index + 1}`,
              content: jsonContent,
              metadata: {
                timestamp: message.created_at
              }
            });
          }
        });
      }

      // Look for tool execution results in intermediate steps
      if (message.metadata?.intermediate_steps) {
        message.metadata.intermediate_steps.forEach((step: any, index: number) => {
          if (step.tool_output) {
            try {
              const toolOutput = typeof step.tool_output === 'string'
                ? JSON.parse(step.tool_output)
                : step.tool_output;

              if (toolOutput.artifact) {
                artifacts.push({
                  id: `${message.id}-tool-${index}`,
                  type: toolOutput.artifact.type || 'text',
                  title: toolOutput.artifact.title || `${step.tool || 'Tool'} Result`,
                  content: toolOutput.artifact.data || toolOutput.artifact.content || toolOutput.artifact,
                  metadata: {
                    ...toolOutput.artifact.metadata,
                    timestamp: message.created_at,
                    tool_name: step.tool,
                    description: toolOutput.message || `Result from ${step.tool}`
                  }
                });
              }
            } catch (e) {
              // Ignore parsing errors for tool outputs
            }
          }
        });
      }
    } catch (error) {
      console.error('Error extracting artifacts:', error);
    }

    return artifacts;
  };

  const artifacts = extractArtifacts();
  const hasArtifacts = artifacts.length > 0;

  const formatContent = (content: string) => {
    // Remove JSON blocks from display content since they're shown as artifacts
    let displayContent = content.replace(/```json\n[\s\S]*?\n```/g, '');

    // Format code blocks
    displayContent = displayContent.replace(
      /```(\w+)?\n([\s\S]*?)\n```/g,
      '<pre class="bg-gray-900 text-green-400 p-3 rounded-lg my-2 overflow-auto"><code>$2</code></pre>'
    );

    // Format inline code
    displayContent = displayContent.replace(
      /`([^`]+)`/g,
      '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>'
    );

    // Format bold text
    displayContent = displayContent.replace(
      /\*\*(.*?)\*\*/g,
      '<strong class="font-semibold">$1</strong>'
    );

    // Format italic text
    displayContent = displayContent.replace(
      /\*(.*?)\*/g,
      '<em class="italic">$1</em>'
    );

    return displayContent;
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-3xl ${isUser ? 'order-2' : 'order-1'}`}>
        {/* Avatar */}
        <div className={`flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold ${
            isUser ? 'bg-primary' : isSystem ? 'bg-gray-500' : 'bg-blue-500'
          }`}>
            {isUser ? 'U' : isSystem ? 'S' : 'AI'}
          </div>

          <div className={`flex-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {/* Message bubble */}
            <div className={`inline-block p-4 rounded-2xl ${
              isUser
                ? 'bg-primary text-white'
                : isSystem
                  ? 'bg-gray-100 text-gray-700'
                  : 'bg-white border border-gray-200 text-text-dark'
            } shadow-sm`}>
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: formatContent(message.content) }}
              />

              {/* Artifacts button */}
              {hasArtifacts && (
                <div className="mt-3 pt-3 border-t border-opacity-20 border-white">
                  <button
                    onClick={() => onViewArtifacts?.(artifacts)}
                    className={`inline-flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                      isUser
                        ? 'bg-white bg-opacity-20 hover:bg-opacity-30 text-white'
                        : 'bg-primary text-white hover:bg-opacity-90'
                    }`}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span>View {artifacts.length} artifact{artifacts.length > 1 ? 's' : ''}</span>
                  </button>
                </div>
              )}
            </div>

            {/* Timestamp */}
            <div className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
              {new Date(message.created_at).toLocaleTimeString()}
            </div>

            {/* Debug info for development */}
            {message.metadata && !message.metadata.success && (
              <div className="mt-2">
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-xs text-red-500 hover:text-red-700"
                >
                  {isExpanded ? 'Hide' : 'Show'} debug info
                </button>
                {isExpanded && (
                  <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded text-xs">
                    <pre className="whitespace-pre-wrap">
                      {JSON.stringify(message.metadata, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
