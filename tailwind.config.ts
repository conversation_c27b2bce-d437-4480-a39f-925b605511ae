import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Diorite color palette
        primary: "#E95B5B", // Vibrant Reddish-Coral
        background: "#FFE6E6", // Soft, Light Blush Pink
        "text-dark": "#4A4A4A", // Dark Slate Grey
        "text-light": "#F8F8F8", // Very light grey
        white: "#FFFFFF",
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [],
} satisfies Config;
