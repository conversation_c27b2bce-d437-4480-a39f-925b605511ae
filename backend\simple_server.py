#!/usr/bin/env python3
"""
Simplified FastAPI server for testing signin functionality.
"""
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Optional
import jwt as pyjwt
from datetime import datetime, timedelta
import uvicorn

# Configuration
SECRET_KEY = "dev-secret-key-for-development-only"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Create FastAPI app
app = FastAPI(title="Diorite API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Pydantic models
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class UserCreate(BaseModel):
    email: str
    username: str
    password: str
    full_name: Optional[str] = None

class User(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool = True

# Mock database
users_db = {
    "admin": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Admin User",
        "hashed_password": "admin123",  # In real app, this would be hashed
        "is_active": True
    }
}

# Helper functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = pyjwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = pyjwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return username
    except pyjwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_current_user(username: str = Depends(verify_token)):
    user = users_db.get(username)
    if user is None:
        raise HTTPException(status_code=401, detail="User not found")
    return user

# Routes
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0", "environment": "development"}

@app.post("/api/v1/auth/login", response_model=Token)
async def login(login_data: LoginRequest):
    user = users_db.get(login_data.username)

    if not user or user["hashed_password"] != login_data.password:
        raise HTTPException(status_code=401, detail="Incorrect username or password")

    if not user["is_active"]:
        raise HTTPException(status_code=400, detail="Inactive user")

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    refresh_token = create_access_token(data={"sub": user["username"]})

    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )

@app.post("/api/v1/auth/register", response_model=User)
async def register(user_data: UserCreate):
    if user_data.username in users_db:
        raise HTTPException(status_code=400, detail="Username already registered")

    # Check if email exists
    for user in users_db.values():
        if user["email"] == user_data.email:
            raise HTTPException(status_code=400, detail="Email already registered")

    # Create new user
    new_user = {
        "id": len(users_db) + 1,
        "username": user_data.username,
        "email": user_data.email,
        "full_name": user_data.full_name,
        "hashed_password": user_data.password,  # In real app, hash this
        "is_active": True
    }

    users_db[user_data.username] = new_user

    return User(**{k: v for k, v in new_user.items() if k != "hashed_password"})

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    return User(**{k: v for k, v in current_user.items() if k != "hashed_password"})

# Additional endpoints for testing
@app.get("/api/v1/datasets/")
async def get_datasets(current_user: dict = Depends(get_current_user)):
    return {
        "datasets": [
            {
                "id": 1,
                "name": "Sample Sales Data",
                "original_filename": "sales_data.csv",
                "file_size": 1024000,
                "rows_count": 1000,
                "columns_count": 8,
                "processing_status": "processed",
                "created_at": "2024-01-01T00:00:00Z",
                "columns_info": [
                    {"name": "date", "dtype": "object"},
                    {"name": "product", "dtype": "object"},
                    {"name": "sales", "dtype": "float64"},
                    {"name": "quantity", "dtype": "int64"},
                    {"name": "price", "dtype": "float64"},
                    {"name": "category", "dtype": "object"},
                    {"name": "region", "dtype": "object"},
                    {"name": "customer_id", "dtype": "int64"}
                ]
            },
            {
                "id": 2,
                "name": "Customer Demographics",
                "original_filename": "customers.csv",
                "file_size": 512000,
                "rows_count": 500,
                "columns_count": 6,
                "processing_status": "processed",
                "created_at": "2024-01-02T00:00:00Z",
                "columns_info": [
                    {"name": "customer_id", "dtype": "int64"},
                    {"name": "age", "dtype": "int64"},
                    {"name": "gender", "dtype": "object"},
                    {"name": "income", "dtype": "float64"},
                    {"name": "city", "dtype": "object"},
                    {"name": "signup_date", "dtype": "object"}
                ]
            }
        ]
    }

@app.get("/api/v1/datasets/{dataset_id}")
async def get_dataset(dataset_id: int, current_user: dict = Depends(get_current_user)):
    if dataset_id == 1:
        return {
            "id": 1,
            "name": "Sample Sales Data",
            "original_filename": "sales_data.csv",
            "file_size": 1024000,
            "rows_count": 1000,
            "columns_count": 8,
            "processing_status": "processed",
            "created_at": "2024-01-01T00:00:00Z",
            "columns_info": [
                {"name": "date", "dtype": "object"},
                {"name": "product", "dtype": "object"},
                {"name": "sales", "dtype": "float64"},
                {"name": "quantity", "dtype": "int64"},
                {"name": "price", "dtype": "float64"},
                {"name": "category", "dtype": "object"},
                {"name": "region", "dtype": "object"},
                {"name": "customer_id", "dtype": "int64"}
            ]
        }
    elif dataset_id == 2:
        return {
            "id": 2,
            "name": "Customer Demographics",
            "original_filename": "customers.csv",
            "file_size": 512000,
            "rows_count": 500,
            "columns_count": 6,
            "processing_status": "processed",
            "created_at": "2024-01-02T00:00:00Z",
            "columns_info": [
                {"name": "customer_id", "dtype": "int64"},
                {"name": "age", "dtype": "int64"},
                {"name": "gender", "dtype": "object"},
                {"name": "income", "dtype": "float64"},
                {"name": "city", "dtype": "object"},
                {"name": "signup_date", "dtype": "object"}
            ]
        }
    else:
        raise HTTPException(status_code=404, detail="Dataset not found")

@app.get("/api/v1/chat/sessions")
async def get_chat_sessions(current_user: dict = Depends(get_current_user)):
    return {"sessions": [], "message": "No chat sessions yet - this is a test endpoint"}

# Manual Analysis endpoints
@app.post("/api/v1/manual-analysis/clean-data")
async def clean_data_manually(request: dict, current_user: dict = Depends(get_current_user)):
    return {
        "success": True,
        "original_shape": [1000, 10],
        "final_shape": [950, 10],
        "cleaning_summary": [
            {"operation": "remove_duplicates", "removed_rows": 50}
        ],
        "message": "Data cleaning completed successfully (demo response)"
    }

@app.post("/api/v1/manual-analysis/create-visualization")
async def create_manual_visualization(request: dict, current_user: dict = Depends(get_current_user)):
    # Mock plot data for demo
    mock_plot_data = {
        "data": [
            {
                "x": [1, 2, 3, 4, 5],
                "y": [2, 4, 3, 5, 6],
                "type": "scatter",
                "mode": "markers",
                "name": "Sample Data"
            }
        ],
        "layout": {
            "title": "Sample Visualization",
            "xaxis": {"title": "X Axis"},
            "yaxis": {"title": "Y Axis"}
        }
    }

    return {
        "success": True,
        "chart_data": {"data": mock_plot_data},
        "dataset_info": {
            "name": "Sample Dataset",
            "filtered_rows": 100,
            "total_rows": 100
        }
    }

@app.post("/api/v1/manual-analysis/train-model")
async def train_model_manually(request: dict, current_user: dict = Depends(get_current_user)):
    return {
        "success": True,
        "algorithm": "random_forest",
        "training_score": 0.95,
        "validation_score": 0.87,
        "training_time_seconds": 2.5,
        "message": "Model training completed successfully (demo response)"
    }

@app.post("/api/v1/manual-analysis/statistical-analysis")
async def perform_statistical_analysis(request: dict, current_user: dict = Depends(get_current_user)):
    return {
        "success": True,
        "analysis_type": "summary",
        "results": {
            "column1": {
                "count": 100,
                "mean": 50.5,
                "std": 15.2,
                "min": 10,
                "max": 90
            }
        },
        "dataset_info": {
            "name": "Sample Dataset",
            "analyzed_columns": ["column1"]
        }
    }

@app.get("/api/v1/manual-analysis/analysis-templates")
async def get_analysis_templates():
    return {
        "templates": {
            "data_cleaning": {
                "name": "Standard Data Cleaning",
                "operations": [
                    {"type": "remove_duplicates"},
                    {"type": "handle_missing", "method": "drop"}
                ]
            }
        }
    }

if __name__ == "__main__":
    print("🚀 Starting Diorite Simple Backend Server...")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔑 Test login: username=admin, password=admin123")
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
