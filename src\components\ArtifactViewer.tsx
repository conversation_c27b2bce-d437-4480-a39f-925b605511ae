'use client';

import React, { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './PlotRenderer';

export interface Artifact {
  id: string;
  type: 'plot' | 'table' | 'code' | 'analysis' | 'text';
  title: string;
  content: any;
  metadata?: {
    chart_type?: string;
    columns?: string[];
    description?: string;
    timestamp?: string;
  };
}

interface ArtifactViewerProps {
  artifacts: Artifact[];
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

const ArtifactViewer: React.FC<ArtifactViewerProps> = ({
  artifacts,
  isOpen,
  onClose,
  className = ""
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  if (!isOpen || artifacts.length === 0) {
    return null;
  }

  const currentArtifact = artifacts[currentIndex];

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : artifacts.length - 1));
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev < artifacts.length - 1 ? prev + 1 : 0));
  };

  const renderArtifactContent = (artifact: Artifact) => {
    switch (artifact.type) {
      case 'plot':
        return (
          <PlotRenderer
            plotData={artifact.content}
            title={artifact.title}
            className="w-full h-96"
          />
        );

      case 'table':
        return (
          <div className="overflow-auto max-h-96">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {artifact.content.columns?.map((col: string, idx: number) => (
                    <th
                      key={idx}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {artifact.content.data?.slice(0, 100).map((row: any[], idx: number) => (
                  <tr key={idx}>
                    {row.map((cell: any, cellIdx: number) => (
                      <td
                        key={cellIdx}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                      >
                        {cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'code':
        return (
          <div className="bg-gray-900 rounded-lg p-4 overflow-auto max-h-96">
            <pre className="text-green-400 text-sm font-mono">
              <code>{artifact.content}</code>
            </pre>
          </div>
        );

      case 'analysis':
        return (
          <div className="space-y-4">
            {Object.entries(artifact.content).map(([key, value]) => (
              <div key={key} className="border-l-4 border-primary pl-4">
                <h4 className="font-semibold text-text-dark capitalize">
                  {key.replace(/_/g, ' ')}
                </h4>
                <div className="text-sm text-gray-600 mt-1">
                  {typeof value === 'object' ? (
                    <pre className="bg-gray-50 p-2 rounded text-xs overflow-auto">
                      {JSON.stringify(value, null, 2)}
                    </pre>
                  ) : (
                    <span>{String(value)}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        );

      case 'text':
      default:
        return (
          <div className="prose max-w-none">
            <div className="text-text-dark whitespace-pre-wrap">
              {artifact.content}
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
      <div className="bg-white rounded-2xl shadow-2xl max-w-6xl max-h-[90vh] w-full mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-text-dark">
              {currentArtifact.title}
            </h2>
            <span className="text-sm text-gray-500">
              {currentIndex + 1} of {artifacts.length}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Navigation buttons */}
            {artifacts.length > 1 && (
              <>
                <button
                  onClick={goToPrevious}
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  title="Previous artifact"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={goToNext}
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  title="Next artifact"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            )}
            
            {/* Close button */}
            <button
              onClick={onClose}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              title="Close"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-auto">
          {renderArtifactContent(currentArtifact)}
          
          {/* Metadata */}
          {currentArtifact.metadata && (
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                {currentArtifact.metadata.chart_type && (
                  <span>Type: {currentArtifact.metadata.chart_type}</span>
                )}
                {currentArtifact.metadata.columns && (
                  <span>Columns: {currentArtifact.metadata.columns.join(', ')}</span>
                )}
                {currentArtifact.metadata.timestamp && (
                  <span>Created: {new Date(currentArtifact.metadata.timestamp).toLocaleString()}</span>
                )}
              </div>
              {currentArtifact.metadata.description && (
                <p className="mt-2 text-sm text-gray-600">
                  {currentArtifact.metadata.description}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Footer with artifact indicators */}
        {artifacts.length > 1 && (
          <div className="flex justify-center p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              {artifacts.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentIndex
                      ? 'bg-primary'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                  title={`Go to artifact ${index + 1}`}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArtifactViewer;
