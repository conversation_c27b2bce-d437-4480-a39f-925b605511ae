"""
Dataset model for managing user data files.
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON, BigInteger
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class Dataset(Base):
    """Dataset model for storing information about uploaded datasets."""
    
    __tablename__ = "datasets"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # File information
    original_filename = Column(String(500), nullable=False)
    file_path = Column(String(1000), nullable=False)
    file_size = Column(BigInteger, nullable=False)  # Size in bytes
    file_type = Column(String(50), nullable=False)  # csv, xlsx, etc.
    
    # Dataset metadata
    rows_count = Column(Integer, nullable=True)
    columns_count = Column(Integer, nullable=True)
    columns_info = Column(JSON, nullable=True)  # Column names, types, etc.
    
    # Processing status
    is_processed = Column(Boolean, default=False)
    processing_status = Column(String(50), default="uploaded")  # uploaded, processing, processed, error
    processing_error = Column(Text, nullable=True)
    
    # Data quality metrics
    missing_values_count = Column(Integer, nullable=True)
    duplicate_rows_count = Column(Integer, nullable=True)
    data_quality_score = Column(Integer, nullable=True)  # 0-100
    
    # Relationships
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    owner = relationship("User", back_populates="datasets")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Dataset(id={self.id}, name='{self.name}', owner_id={self.owner_id})>"


class DatasetVersion(Base):
    """Dataset version model for tracking dataset modifications."""
    
    __tablename__ = "dataset_versions"
    
    id = Column(Integer, primary_key=True, index=True)
    dataset_id = Column(Integer, ForeignKey("datasets.id"), nullable=False)
    version_number = Column(Integer, nullable=False)
    description = Column(Text, nullable=True)
    
    # File information for this version
    file_path = Column(String(1000), nullable=False)
    file_size = Column(BigInteger, nullable=False)
    
    # Changes made
    changes_description = Column(Text, nullable=True)
    transformation_steps = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    dataset = relationship("Dataset")
    
    def __repr__(self):
        return f"<DatasetVersion(id={self.id}, dataset_id={self.dataset_id}, version={self.version_number})>"
