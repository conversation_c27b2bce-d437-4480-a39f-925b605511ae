# Diorite Backend - AI Data Science Platform

A robust, scalable FastAPI backend for the Diorite AI Data Science Platform, featuring multi-user support, AI agent integration, and comprehensive data science capabilities.

## 🚀 Features

### Core Functionality
- **Multi-user Authentication**: JWT-based secure authentication and authorization
- **Dataset Management**: Upload, process, and manage CSV/Excel/JSON datasets
- **AI Agent Integration**: LangChain-powered AI assistant for data science tasks
- **ML Model Training**: Automated machine learning with scikit-learn
- **Data Visualization**: Interactive charts and plots with Plotly
- **Real-time Chat**: WebSocket support for AI agent conversations
- **Background Tasks**: Celery-based task queue for long-running operations

### Architecture Components
- **FastAPI**: Modern, fast web framework for building APIs
- **PostgreSQL**: Robust relational database for data persistence
- **Redis**: In-memory data store for caching and task queues
- **Celery**: Distributed task queue for background processing
- **SQLAlchemy**: Python SQL toolkit and ORM
- **Lang<PERSON>hain**: Framework for developing AI agent applications
- **Docker**: Containerization for easy deployment

## 📋 Prerequisites

- Python 3.11+
- Docker and Docker Compose
- OpenAI API key (for AI agent functionality)

## 🛠️ Installation & Setup

### 1. Clone and Setup Environment

```bash
cd backend
cp .env.example .env
```

### 2. Configure Environment Variables

Edit `.env` file with your configuration:

```env
# Required: Add your OpenAI API key
OPENAI_API_KEY=your-openai-api-key-here

# Database (auto-configured for Docker)
DATABASE_URL=postgresql://diorite_user:diorite_password@localhost:5432/diorite_db

# Security (change in production)
SECRET_KEY=your-super-secret-key-change-this-in-production

# Other settings are pre-configured for development
```

### 3. Start Development Environment

```bash
# Make scripts executable
chmod +x scripts/*.sh

# Start all services with Docker Compose
./scripts/start-dev.sh
```

This will start:
- PostgreSQL database (port 5432)
- Redis (port 6379)
- MinIO file storage (ports 9000, 9001)
- FastAPI backend (port 8000)
- Celery worker
- Celery beat scheduler
- Flower monitoring (port 5555)

## 📚 API Documentation

Once running, access the interactive API documentation:

- **Swagger UI**: http://localhost:8000/api/v1/docs
- **ReDoc**: http://localhost:8000/api/v1/redoc

## 🔧 Development

### Project Structure

```
backend/
├── app/
│   ├── api/v1/           # API endpoints
│   ├── core/             # Core configuration
│   ├── models/           # SQLAlchemy models
│   ├── schemas/          # Pydantic schemas
│   ├── services/         # Business logic services
│   ├── tasks/            # Celery tasks
│   └── main.py           # FastAPI application
├── alembic/              # Database migrations
├── scripts/              # Utility scripts
├── uploads/              # File storage (development)
├── docker-compose.yml    # Docker services
├── Dockerfile            # Container definition
└── requirements.txt      # Python dependencies
```

### Key Services

#### Authentication Service
- JWT-based authentication
- User registration and login
- Password hashing with bcrypt
- Token refresh mechanism

#### Dataset Service
- File upload validation
- Automatic data profiling
- Data quality scoring
- Dataset versioning

#### AI Agent Service
- LangChain-powered conversational AI
- Custom tools for data science tasks
- Context-aware conversations
- Tool execution tracking

#### ML Service
- Automated model training
- Multiple algorithms support
- Cross-validation and metrics
- Model persistence and prediction

### Database Models

- **User**: User accounts and profiles
- **Dataset**: Dataset metadata and file information
- **MLModel**: Trained model information and metrics
- **ChatSession**: AI agent conversation sessions
- **ChatMessage**: Individual chat messages

## 🔄 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Token refresh
- `GET /api/v1/auth/me` - Current user info

### Datasets
- `POST /api/v1/datasets/upload` - Upload dataset
- `GET /api/v1/datasets/` - List user datasets
- `GET /api/v1/datasets/{id}` - Get dataset details
- `GET /api/v1/datasets/{id}/preview` - Preview dataset
- `GET /api/v1/datasets/{id}/stats` - Dataset statistics

### ML Models
- `POST /api/v1/models/` - Create model
- `GET /api/v1/models/` - List user models
- `POST /api/v1/models/{id}/train` - Train model
- `POST /api/v1/models/{id}/predict` - Make predictions

### AI Chat
- `POST /api/v1/chat/sessions` - Create chat session
- `GET /api/v1/chat/sessions` - List chat sessions
- `POST /api/v1/chat/sessions/{id}/messages` - Send message
- `GET /api/v1/chat/sessions/{id}/messages` - Get messages

## 🧪 Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest
```

## 📊 Monitoring

### Celery Tasks
Monitor background tasks at: http://localhost:5555

### Database
Connect to PostgreSQL:
```bash
psql -h localhost -p 5432 -U diorite_user -d diorite_db
```

### File Storage
MinIO console: http://localhost:9001 (admin/minioadmin)

## 🚀 Production Deployment

### Environment Variables
Update `.env` for production:
```env
ENVIRONMENT=production
DEBUG=False
SECRET_KEY=your-production-secret-key
DATABASE_URL=your-production-database-url
ALLOWED_ORIGINS=https://yourdomain.com
```

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🔒 Security Features

- JWT token authentication
- Password hashing with bcrypt
- CORS protection
- Input validation with Pydantic
- SQL injection prevention with SQLAlchemy
- File upload validation
- User data isolation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the API documentation
2. Review the logs in Docker containers
3. Check Celery task status in Flower
4. Verify environment configuration

## 🔄 Updates

The backend is designed to be:
- **Scalable**: Horizontal scaling with load balancers
- **Maintainable**: Clean architecture and comprehensive logging
- **Extensible**: Plugin-based AI tools and modular services
- **Secure**: Industry-standard security practices
