#!/bin/bash

# Diorite Full-Stack Startup Script

set -e

echo "🚀 Starting Diorite AI Data Science Platform..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Python
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install Python 3.11+ first."
    exit 1
fi

# Check Docker
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

print_success "All prerequisites are available!"

# Setup backend
print_status "Setting up backend..."
cd backend

# Check if .env exists
if [ ! -f .env ]; then
    print_warning "Backend .env file not found. Creating from template..."
    cp .env.example .env
    print_warning "Please edit backend/.env and add your OPENAI_API_KEY before continuing."
    read -p "Press Enter after you've updated the .env file..."
fi

# Make scripts executable
chmod +x scripts/*.sh

# Start backend services
print_status "Starting backend services (PostgreSQL, Redis, MinIO)..."
docker-compose up -d postgres redis minio

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 15

# Check if services are healthy
print_status "Checking service health..."
if ! docker-compose ps | grep -q "Up (healthy)"; then
    print_warning "Some services may not be fully ready. Continuing anyway..."
fi

# Start backend in background
print_status "Starting FastAPI backend..."
./scripts/start-dev.sh &
BACKEND_PID=$!

# Wait for backend to be ready
print_status "Waiting for backend to be ready..."
sleep 10

cd ..

# Setup frontend
print_status "Setting up frontend..."

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Start frontend
print_status "Starting Next.js frontend..."
npm run dev &
FRONTEND_PID=$!

# Wait for frontend to be ready
sleep 5

# Display status
echo ""
print_success "🎉 Diorite is now running!"
echo ""
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000/api/v1/docs"
echo "🌸 Celery Monitor: http://localhost:5555"
echo "💾 MinIO Console: http://localhost:9001 (admin/minioadmin)"
echo ""
print_status "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    print_status "Shutting down services..."
    
    # Kill frontend
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Kill backend
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    # Stop Docker services
    cd backend
    docker-compose down
    
    print_success "All services stopped. Goodbye! 👋"
}

# Set trap to cleanup on exit
trap cleanup EXIT

# Wait for user to stop
wait
