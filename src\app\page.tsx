'use client';

import React, { useState } from 'react';
import { AuthProvider } from '@/contexts/AuthContext';
import Sidebar from '@/components/Sidebar';
import MainContent from '@/components/MainContent';
import ChatInterface from '@/components/ChatInterface';
import DataScienceDashboard from '@/components/DataScienceDashboard';
import AuthModal from '@/components/AuthModal';
import { useAuth } from '@/contexts/AuthContext';

// Main App Component
const AppContent: React.FC = () => {
  const [currentPage, setCurrentPage] = useState('home');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const { isAuthenticated, user } = useAuth();

  const handleNavigate = (page: string) => {
    if (!isAuthenticated && page !== 'home') {
      setIsAuthModalOpen(true);
      return;
    }
    setCurrentPage(page);
  };

  const renderCurrentPage = () => {
    if (!isAuthenticated && currentPage !== 'home') {
      return <MainContent />;
    }

    switch (currentPage) {
      case 'workspace':
        return <DataScienceDashboard />;
      case 'chat':
        return <ChatInterface />;
      case 'data':
        return (
          <div className="ml-20 min-h-screen bg-background flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">📊</div>
              <h2 className="text-2xl font-semibold text-text-dark mb-2">Datasets</h2>
              <p className="text-gray-600">Dataset management coming soon...</p>
            </div>
          </div>
        );
      case 'models':
        return (
          <div className="ml-20 min-h-screen bg-background flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">🧠</div>
              <h2 className="text-2xl font-semibold text-text-dark mb-2">ML Models</h2>
              <p className="text-gray-600">Model management coming soon...</p>
            </div>
          </div>
        );
      case 'charts':
        return (
          <div className="ml-20 min-h-screen bg-background p-8">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-text-dark mb-8">Visualizations</h2>
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <h3 className="text-xl font-semibold mb-4">Demo: Plot Rendering</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-2">Sample Sales Chart</h4>
                    <div className="h-64 border border-gray-200 rounded-lg p-4">
                      <div className="w-full h-full bg-gray-50 rounded flex items-center justify-center">
                        <span className="text-gray-500">Chart will render here</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Instructions</h4>
                    <div className="text-sm text-gray-600 space-y-2">
                      <p>• Upload a dataset from the home page</p>
                      <p>• Go to AI Chat and ask for visualizations</p>
                      <p>• Click "View Artifacts" to see interactive plots</p>
                      <p>• Use navigation arrows to browse multiple artifacts</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'profile':
        return (
          <div className="ml-20 min-h-screen bg-background flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">👤</div>
              <h2 className="text-2xl font-semibold text-text-dark mb-2">Profile</h2>
              <p className="text-gray-600">User profile coming soon...</p>
            </div>
          </div>
        );
      case 'home':
      default:
        return <MainContent />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Sidebar activeIcon={currentPage} onNavigate={handleNavigate} />
      {renderCurrentPage()}

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />

      {/* Login prompt for unauthenticated users */}
      {!isAuthenticated && currentPage === 'home' && (
        <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4 border border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="text-primary text-2xl">🔐</div>
            <div>
              <p className="font-medium text-text-dark">Ready to get started?</p>
              <p className="text-sm text-gray-600">Sign in to access all features</p>
            </div>
            <button
              onClick={() => setIsAuthModalOpen(true)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-colors"
            >
              Sign In
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Root component with providers
export default function Home() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}
