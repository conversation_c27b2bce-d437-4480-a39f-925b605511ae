"""
Celery application configuration.
"""
from celery import Celery
from app.core.config import settings

# Create Celery instance
celery_app = Celery(
    "diorite",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.tasks.dataset_tasks",
        "app.tasks.model_tasks",
        "app.tasks.agent_tasks"
    ]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Task routing
celery_app.conf.task_routes = {
    "app.tasks.dataset_tasks.*": {"queue": "dataset_processing"},
    "app.tasks.model_tasks.*": {"queue": "model_training"},
    "app.tasks.agent_tasks.*": {"queue": "agent_processing"},
}

if __name__ == "__main__":
    celery_app.start()
