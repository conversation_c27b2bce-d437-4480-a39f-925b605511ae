'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface SidebarProps {
  activeIcon?: string;
  onNavigate?: (page: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeIcon = 'home', onNavigate }) => {
  const [hoveredIcon, setHoveredIcon] = useState<string | null>(null);
  const { user, logout } = useAuth();

  const navigationIcons = [
    { id: 'home', icon: '🏠', label: 'Home' },
    { id: 'workspace', icon: '🔬', label: 'Data Science Workspace' },
    { id: 'chat', icon: '💬', label: 'AI Chat' },
    { id: 'data', icon: '📊', label: 'Datasets' },
    { id: 'models', icon: '🧠', label: 'ML Models' },
    { id: 'charts', icon: '📈', label: 'Visualizations' },
  ];

  const IconButton = ({
    id,
    icon,
    label,
    isActive,
    isHovered,
    onClick
  }: {
    id: string;
    icon: string;
    label: string;
    isActive: boolean;
    isHovered: boolean;
    onClick?: () => void;
  }) => (
    <button
      className={`
        w-12 h-12 flex items-center justify-center rounded-lg
        transition-all duration-200 ease-in-out relative group
        ${isActive
          ? 'bg-white bg-opacity-20 scale-105'
          : isHovered
            ? 'bg-white bg-opacity-10 scale-105'
            : 'hover:bg-white hover:bg-opacity-10 hover:scale-105'
        }
      `}
      onMouseEnter={() => setHoveredIcon(id)}
      onMouseLeave={() => setHoveredIcon(null)}
      onClick={onClick}
      aria-label={label}
    >
      <span className="text-white text-xl">{icon}</span>

      {/* Tooltip */}
      <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
        {label}
      </div>
    </button>
  );

  const handleLogout = () => {
    logout();
    onNavigate?.('home');
  };

  return (
    <div className="fixed left-0 top-0 h-full w-20 bg-primary flex flex-col items-center py-6 z-40">
      {/* Menu Icon / Logo */}
      <button
        className="w-12 h-12 flex items-center justify-center rounded-lg mb-8 hover:bg-white hover:bg-opacity-10 transition-all duration-200 group"
        aria-label="Menu"
        onClick={() => onNavigate?.('home')}
      >
        <div className="flex flex-col space-y-1">
          <div className="w-5 h-0.5 bg-white"></div>
          <div className="w-5 h-0.5 bg-white"></div>
          <div className="w-5 h-0.5 bg-white"></div>
        </div>

        {/* Tooltip */}
        <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
          Diorite
        </div>
      </button>

      {/* Navigation Icons */}
      <div className="flex flex-col space-y-4 flex-1">
        {navigationIcons.map((item) => (
          <IconButton
            key={item.id}
            id={item.id}
            icon={item.icon}
            label={item.label}
            isActive={activeIcon === item.id}
            isHovered={hoveredIcon === item.id}
            onClick={() => onNavigate?.(item.id)}
          />
        ))}
      </div>

      {/* User Section */}
      <div className="flex flex-col space-y-2">
        {user && (
          <button
            className="w-12 h-12 flex items-center justify-center rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200 group"
            aria-label="Profile"
            onClick={() => onNavigate?.('profile')}
          >
            <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-semibold">
                {(user.full_name || user.username).charAt(0).toUpperCase()}
              </span>
            </div>

            {/* Tooltip */}
            <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              {user.full_name || user.username}
            </div>
          </button>
        )}

        {/* Settings/Logout Icon */}
        <button
          className="w-12 h-12 flex items-center justify-center rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200 group"
          aria-label={user ? "Logout" : "Settings"}
          onClick={user ? handleLogout : () => onNavigate?.('settings')}
        >
          <span className="text-white text-xl">{user ? '🚪' : '⚙️'}</span>

          {/* Tooltip */}
          <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            {user ? 'Logout' : 'Settings'}
          </div>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
