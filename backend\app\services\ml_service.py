"""
Machine Learning service for model training and prediction.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import joblib
import json
from pathlib import Path
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.svm import SVC, SVR
from sklearn.cluster import KMeans
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    mean_squared_error, mean_absolute_error, r2_score, confusion_matrix
)
from sklearn.preprocessing import LabelEncoder, StandardScaler
import time

from app.core.config import settings
from app.services.file_manager import file_manager


class MLService:
    """Machine Learning service for training and prediction."""
    
    def __init__(self):
        self.algorithms = {
            # Classification
            "random_forest_classifier": RandomForestClassifier,
            "logistic_regression": LogisticRegression,
            "svm_classifier": SVC,
            
            # Regression
            "random_forest_regressor": RandomForestRegressor,
            "linear_regression": LinearRegression,
            "svm_regressor": SVR,
            
            # Clustering
            "kmeans": KMeans
        }
        
        self.default_hyperparameters = {
            "random_forest_classifier": {"n_estimators": 100, "random_state": 42},
            "random_forest_regressor": {"n_estimators": 100, "random_state": 42},
            "logistic_regression": {"random_state": 42, "max_iter": 1000},
            "linear_regression": {},
            "svm_classifier": {"random_state": 42, "probability": True},
            "svm_regressor": {},
            "kmeans": {"n_clusters": 3, "random_state": 42}
        }
    
    def train_model(
        self,
        df: pd.DataFrame,
        algorithm: str,
        model_type: str,
        target_column: Optional[str] = None,
        feature_columns: Optional[List[str]] = None,
        hyperparameters: Optional[Dict[str, Any]] = None,
        validation_split: float = 0.2,
        test_split: float = 0.2,
        user_id: int = None
    ) -> Dict[str, Any]:
        """Train a machine learning model."""
        
        if algorithm not in self.algorithms:
            raise ValueError(f"Unsupported algorithm: {algorithm}")
        
        start_time = time.time()
        
        # Prepare data
        if model_type in ["classification", "regression"]:
            if not target_column or target_column not in df.columns:
                raise ValueError("Target column is required for supervised learning")
            
            if feature_columns:
                X = df[feature_columns]
            else:
                X = df.drop(columns=[target_column])
            
            y = df[target_column]
            
            # Handle categorical features
            X = self._preprocess_features(X)
            
            # Handle categorical target for classification
            if model_type == "classification" and y.dtype == 'object':
                label_encoder = LabelEncoder()
                y = label_encoder.fit_transform(y)
            
            # Split data
            X_temp, X_test, y_temp, y_test = train_test_split(
                X, y, test_size=test_split, random_state=42
            )
            
            X_train, X_val, y_train, y_val = train_test_split(
                X_temp, y_temp, test_size=validation_split/(1-test_split), random_state=42
            )
            
        else:  # clustering
            if feature_columns:
                X = df[feature_columns]
            else:
                X = df.select_dtypes(include=[np.number])
            
            X = self._preprocess_features(X)
            X_train = X
            y_train = None
        
        # Get model class and hyperparameters
        model_class = self.algorithms[algorithm]
        params = self.default_hyperparameters.get(algorithm, {})
        if hyperparameters:
            params.update(hyperparameters)
        
        # Train model
        model = model_class(**params)
        
        if model_type in ["classification", "regression"]:
            model.fit(X_train, y_train)
        else:  # clustering
            model.fit(X_train)
        
        # Calculate metrics
        metrics = self._calculate_metrics(
            model, model_type, X_train, y_train,
            X_val if model_type != "clustering" else None,
            y_val if model_type != "clustering" else None,
            X_test if model_type != "clustering" else None,
            y_test if model_type != "clustering" else None
        )
        
        # Save model
        model_filename = f"model_{algorithm}_{int(time.time())}.joblib"
        model_path = file_manager.get_user_directory(user_id) / "models" / model_filename
        model_path.parent.mkdir(parents=True, exist_ok=True)
        
        joblib.dump(model, model_path)
        
        training_time = time.time() - start_time
        
        return {
            "model_path": str(model_path),
            "training_score": metrics.get("training_score"),
            "validation_score": metrics.get("validation_score"),
            "test_score": metrics.get("test_score"),
            "metrics": metrics,
            "training_time_seconds": training_time,
            "feature_columns": list(X.columns) if hasattr(X, 'columns') else feature_columns
        }
    
    def predict(self, model_path: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make predictions with a trained model."""
        try:
            # Load model
            model = joblib.load(model_path)
            
            # Convert input to DataFrame
            input_df = pd.DataFrame([input_data])
            
            # Preprocess input
            input_processed = self._preprocess_features(input_df)
            
            # Make prediction
            prediction = model.predict(input_processed)
            
            # Get prediction probability if available
            confidence = None
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(input_processed)
                confidence = float(np.max(proba))
            elif hasattr(model, 'decision_function'):
                decision = model.decision_function(input_processed)
                confidence = float(np.abs(decision[0]))
            
            return {
                "prediction": prediction.tolist(),
                "confidence": confidence
            }
            
        except Exception as e:
            raise Exception(f"Prediction failed: {str(e)}")
    
    def _preprocess_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Preprocess features for training/prediction."""
        X_processed = X.copy()
        
        # Handle categorical variables
        categorical_columns = X_processed.select_dtypes(include=['object', 'category']).columns
        
        for col in categorical_columns:
            # Simple label encoding for now
            le = LabelEncoder()
            X_processed[col] = le.fit_transform(X_processed[col].astype(str))
        
        # Handle missing values
        X_processed = X_processed.fillna(X_processed.mean())
        
        return X_processed
    
    def _calculate_metrics(
        self,
        model,
        model_type: str,
        X_train,
        y_train,
        X_val=None,
        y_val=None,
        X_test=None,
        y_test=None
    ) -> Dict[str, Any]:
        """Calculate model performance metrics."""
        metrics = {}
        
        if model_type == "classification":
            # Training score
            train_pred = model.predict(X_train)
            metrics["training_score"] = float(accuracy_score(y_train, train_pred))
            
            if X_val is not None and y_val is not None:
                val_pred = model.predict(X_val)
                metrics["validation_score"] = float(accuracy_score(y_val, val_pred))
                metrics["precision"] = float(precision_score(y_val, val_pred, average='weighted'))
                metrics["recall"] = float(recall_score(y_val, val_pred, average='weighted'))
                metrics["f1_score"] = float(f1_score(y_val, val_pred, average='weighted'))
                
                if hasattr(model, 'predict_proba'):
                    val_proba = model.predict_proba(X_val)
                    if val_proba.shape[1] == 2:  # Binary classification
                        metrics["roc_auc"] = float(roc_auc_score(y_val, val_proba[:, 1]))
                
                metrics["confusion_matrix"] = confusion_matrix(y_val, val_pred).tolist()
            
            if X_test is not None and y_test is not None:
                test_pred = model.predict(X_test)
                metrics["test_score"] = float(accuracy_score(y_test, test_pred))
        
        elif model_type == "regression":
            # Training score
            train_pred = model.predict(X_train)
            metrics["training_score"] = float(r2_score(y_train, train_pred))
            
            if X_val is not None and y_val is not None:
                val_pred = model.predict(X_val)
                metrics["validation_score"] = float(r2_score(y_val, val_pred))
                metrics["mse"] = float(mean_squared_error(y_val, val_pred))
                metrics["rmse"] = float(np.sqrt(mean_squared_error(y_val, val_pred)))
                metrics["mae"] = float(mean_absolute_error(y_val, val_pred))
            
            if X_test is not None and y_test is not None:
                test_pred = model.predict(X_test)
                metrics["test_score"] = float(r2_score(y_test, test_pred))
        
        elif model_type == "clustering":
            # For clustering, use inertia as a metric
            if hasattr(model, 'inertia_'):
                metrics["inertia"] = float(model.inertia_)
            
            # Silhouette score could be added here
            metrics["n_clusters"] = getattr(model, 'n_clusters', None)
        
        # Feature importance if available
        if hasattr(model, 'feature_importances_'):
            feature_names = [f"feature_{i}" for i in range(len(model.feature_importances_))]
            metrics["feature_importance"] = dict(zip(feature_names, model.feature_importances_.tolist()))
        
        return metrics


# Global ML service instance
ml_service = MLService()
