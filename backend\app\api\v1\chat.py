"""
AI Chat endpoints for agent interaction.
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
import json

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.chat import ChatSession, ChatMessage
from app.models.dataset import Dataset
from app.schemas.chat import (
    ChatSessionCreate,
    ChatSession as ChatSessionSchema,
    ChatMessageCreate,
    ChatMessage as ChatMessageSchema,
    ChatResponse
)
from app.services.ai_agent import data_science_agent
import structlog

logger = structlog.get_logger()
router = APIRouter()


@router.post("/sessions", response_model=ChatSessionSchema)
def create_chat_session(
    session_data: ChatSessionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Create a new chat session."""
    session = ChatSession(
        title=session_data.title,
        context_data=session_data.context_data,
        user_id=current_user.id
    )
    
    db.add(session)
    db.commit()
    db.refresh(session)
    
    return session


@router.get("/sessions", response_model=List[ChatSessionSchema])
def get_chat_sessions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get user's chat sessions."""
    sessions = db.query(ChatSession).filter(
        ChatSession.user_id == current_user.id,
        ChatSession.is_active == True
    ).order_by(ChatSession.updated_at.desc()).all()
    
    return sessions


@router.get("/sessions/{session_id}", response_model=ChatSessionSchema)
def get_chat_session(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get a specific chat session."""
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    return session


@router.get("/sessions/{session_id}/messages", response_model=List[ChatMessageSchema])
def get_chat_messages(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get messages from a chat session."""
    # Verify session ownership
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(ChatMessage.created_at.asc()).all()
    
    return messages


@router.post("/sessions/{session_id}/messages", response_model=ChatResponse)
def send_message(
    session_id: int,
    message_data: ChatMessageCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Send a message to the AI agent."""
    # Verify session ownership
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    # Save user message
    user_message = ChatMessage(
        session_id=session_id,
        content=message_data.content,
        message_type="user"
    )
    db.add(user_message)
    db.commit()
    db.refresh(user_message)
    
    try:
        # Prepare context for agent
        context = session.context_data or {}
        
        # Add available datasets to context
        user_datasets = db.query(Dataset).filter(
            Dataset.owner_id == current_user.id,
            Dataset.is_processed == True
        ).all()
        
        context["available_datasets"] = [
            {"id": d.id, "name": d.name, "columns": d.columns_count, "rows": d.rows_count}
            for d in user_datasets
        ]
        
        # Process message with AI agent
        agent_response = data_science_agent.process_message(
            message_data.content,
            current_user.id,
            context
        )
        
        # Save agent response
        agent_message = ChatMessage(
            session_id=session_id,
            content=agent_response["response"],
            message_type="assistant",
            metadata={
                "success": agent_response["success"],
                "intermediate_steps": agent_response.get("intermediate_steps", [])
            }
        )
        db.add(agent_message)
        
        # Update session
        session.updated_at = db.func.now()
        db.commit()
        db.refresh(agent_message)
        
        return ChatResponse(
            user_message=user_message,
            agent_message=agent_message,
            success=agent_response["success"]
        )
        
    except Exception as e:
        logger.error("Error processing chat message", 
                    session_id=session_id, 
                    user_id=current_user.id, 
                    error=str(e))
        
        # Save error message
        error_message = ChatMessage(
            session_id=session_id,
            content=f"I encountered an error: {str(e)}",
            message_type="assistant",
            metadata={"success": False, "error": str(e)}
        )
        db.add(error_message)
        db.commit()
        db.refresh(error_message)
        
        return ChatResponse(
            user_message=user_message,
            agent_message=error_message,
            success=False
        )


@router.delete("/sessions/{session_id}")
def delete_chat_session(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Delete a chat session."""
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    # Soft delete
    session.is_active = False
    db.commit()
    
    return {"message": "Chat session deleted successfully"}


# WebSocket endpoint for real-time chat
@router.websocket("/sessions/{session_id}/ws")
async def websocket_chat(
    websocket: WebSocket,
    session_id: int,
    db: Session = Depends(get_db)
):
    """WebSocket endpoint for real-time chat."""
    await websocket.accept()
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Process message (simplified - would need proper auth)
            response = {
                "type": "message",
                "content": f"Echo: {message_data.get('content', '')}",
                "timestamp": "2024-01-01T00:00:00Z"
            }
            
            # Send response
            await websocket.send_text(json.dumps(response))
            
    except WebSocketDisconnect:
        logger.info("WebSocket disconnected", session_id=session_id)
    except Exception as e:
        logger.error("WebSocket error", session_id=session_id, error=str(e))
        await websocket.close()
