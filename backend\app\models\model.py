"""
ML Model management for storing trained models and their metadata.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class MLModel(Base):
    """ML Model for storing information about trained models."""
    
    __tablename__ = "ml_models"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Model information
    model_type = Column(String(100), nullable=False)  # classification, regression, clustering
    algorithm = Column(String(100), nullable=False)  # random_forest, svm, kmeans, etc.
    
    # File paths
    model_file_path = Column(String(1000), nullable=False)  # Serialized model file
    
    # Training information
    dataset_id = Column(Integer, ForeignKey("datasets.id"), nullable=False)
    target_column = Column(String(255), nullable=True)  # For supervised learning
    feature_columns = Column(JSON, nullable=True)  # List of feature column names
    
    # Model performance metrics
    training_score = Column(Float, nullable=True)
    validation_score = Column(Float, nullable=True)
    test_score = Column(Float, nullable=True)
    metrics = Column(JSON, nullable=True)  # Additional metrics (precision, recall, etc.)
    
    # Training parameters
    hyperparameters = Column(JSON, nullable=True)
    training_time_seconds = Column(Float, nullable=True)
    
    # Model status
    status = Column(String(50), default="training")  # training, completed, failed
    training_error = Column(Text, nullable=True)
    
    # Relationships
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    owner = relationship("User", back_populates="models")
    dataset = relationship("Dataset")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<MLModel(id={self.id}, name='{self.name}', algorithm='{self.algorithm}')>"


class ModelPrediction(Base):
    """Store model predictions for tracking and analysis."""
    
    __tablename__ = "model_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey("ml_models.id"), nullable=False)
    
    # Input data
    input_data = Column(JSON, nullable=False)  # The input features
    prediction = Column(JSON, nullable=False)  # The prediction result
    confidence_score = Column(Float, nullable=True)  # Confidence/probability
    
    # Metadata
    prediction_time = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    model = relationship("MLModel")
    
    def __repr__(self):
        return f"<ModelPrediction(id={self.id}, model_id={self.model_id})>"
