# Diorite - AI Data Science Platform

A comprehensive, multi-user AI-powered data science platform built with Next.js frontend and FastAPI backend, featuring intelligent data analysis, machine learning, and conversational AI assistance.

## 🌟 Features

### Frontend (Next.js)
- **Modern UI/UX**: Clean, intuitive interface with coral/pink design theme
- **Fixed Sidebar Navigation**: Easy access to all platform features
- **Responsive Design**: Works seamlessly across desktop and mobile devices
- **Real-time Updates**: Live data and chat interactions

### Backend (FastAPI)
- **Multi-user Support**: Secure user authentication and data isolation
- **AI Agent Integration**: LangChain-powered conversational AI assistant
- **Dataset Management**: Upload, process, and analyze CSV/Excel/JSON files
- **Machine Learning**: Automated model training and prediction
- **Data Visualization**: Interactive charts and plots
- **Background Processing**: Celery-based task queue for long-running operations

### AI Capabilities
- **Conversational Interface**: Natural language interaction with data
- **Automated Insights**: AI-generated data analysis and recommendations
- **Code Execution**: Safe Python code execution in sandboxed environment
- **Smart Suggestions**: Context-aware assistance for data science tasks

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js       │    │   FastAPI       │    │   PostgreSQL    │
│   Frontend      │◄──►│   Backend       │◄──►│   Database      │
│   (Port 3000)   │    │   (Port 8000)   │    │   (Port 5432)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Redis         │    │   MinIO         │
                       │   Cache/Queue   │    │   File Storage  │
                       │   (Port 6379)   │    │   (Port 9000)   │
                       └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Celery        │
                       │   Workers       │
                       │   (Background)  │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Python 3.11+
- Docker and Docker Compose
- OpenAI API key

### 1. Clone Repository
```bash
git clone <repository-url>
cd diorite
```

### 2. Setup Backend
```bash
cd backend
cp .env.example .env
# Edit .env and add your OPENAI_API_KEY
chmod +x scripts/*.sh
./scripts/start-dev.sh
```

### 3. Setup Frontend
```bash
cd ../
npm install
npm run dev
```

### 4. Access the Platform
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000/api/v1/docs
- **Celery Monitor**: http://localhost:5555
- **MinIO Console**: http://localhost:9001

### 🚀 Quick Start (One Command)
```bash
# Start everything with one script
chmod +x start-diorite.sh
./start-diorite.sh
```

## 📱 Frontend Features

### 🎨 Artifact System
The platform features a sophisticated artifact system for displaying AI-generated content:

#### Artifact Types
- **📊 Plots**: Interactive Plotly.js charts and visualizations
- **📋 Tables**: Formatted data tables with pagination
- **🧠 Analysis**: Statistical analysis and insights
- **💻 Code**: Syntax-highlighted code blocks
- **📄 Text**: Formatted text and markdown content

#### Navigation Features
- **Forward/Backward**: Navigate through multiple artifacts with arrow buttons
- **Full-Screen View**: Expand artifacts for detailed viewing
- **Metadata Display**: Show creation time, data sources, descriptions
- **Interactive Charts**: Zoom, pan, and explore Plotly visualizations

### 💬 AI Chat Interface
- **Real-time Messaging**: Instant communication with AI agent
- **Artifact Integration**: Plots and analysis results embedded in chat
- **Session Management**: Multiple chat sessions with history
- **Context Awareness**: AI remembers uploaded datasets and previous conversations

### 🔐 Authentication & Security
- **JWT-based Auth**: Secure token authentication
- **User Registration**: Easy account creation with validation
- **Data Isolation**: Each user can only access their own data
- **Session Management**: Automatic token refresh and logout

### 📁 File Management
- **Drag & Drop Upload**: Intuitive file upload interface
- **Progress Tracking**: Real-time upload progress indicators
- **File Validation**: Type and size validation before upload
- **History Panel**: View recently uploaded datasets with status

## 🛠️ Technical Implementation

### Frontend Stack
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Plotly.js**: Interactive data visualizations
- **Context API**: State management for authentication

### Backend Integration
- **REST API**: Full integration with FastAPI backend
- **Real-time Updates**: WebSocket support for chat
- **Error Handling**: Comprehensive error management
- **Type Safety**: TypeScript interfaces for API responses

## 🎯 Usage Examples

### 1. Upload and Analyze Data
```
1. Go to Home page
2. Drag and drop a CSV file
3. Wait for processing to complete
4. Navigate to AI Chat
5. Ask: "Analyze my dataset and create visualizations"
```

### 2. Create Interactive Plots
```
1. In AI Chat, type: "Create a scatter plot of sales vs revenue"
2. Click "View Artifacts" when response arrives
3. Use navigation arrows to browse multiple charts
4. Interact with plots (zoom, pan, hover for details)
```

### 3. Get Data Insights
```
1. Upload your dataset
2. Ask AI: "What are the key insights from this data?"
3. View analysis artifacts with statistics and recommendations
4. Navigate between different analysis results
```

## 🔧 Development

### Frontend Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Type checking
npm run type-check
```

### Environment Variables
```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
```

---

**Diorite** - Empowering data scientists with AI-driven insights and seamless collaboration.
