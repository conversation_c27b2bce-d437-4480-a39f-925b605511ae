'use client';

import React, { useState } from 'react';

interface Dataset {
  id: number;
  name: string;
  original_filename: string;
  file_size: number;
  rows_count?: number;
  columns_count?: number;
  processing_status: string;
  created_at: string;
}

interface HistoryPanelProps {
  datasets?: Dataset[];
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({ datasets = [] }) => {
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="bg-primary rounded-2xl p-6 w-80 h-96 flex flex-col">
      {/* Title */}
      <h2 className="text-white font-bold text-xl mb-6 tracking-wide">
        HISTORY
      </h2>

      {/* Dataset List */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-3">
          {datasets.map((dataset) => (
            <button
              key={dataset.id}
              className={`
                w-full text-left p-3 rounded-lg transition-all duration-200
                ${hoveredItem === dataset.id
                  ? 'bg-white bg-opacity-15 transform translate-x-1'
                  : 'hover:bg-white hover:bg-opacity-10 hover:transform hover:translate-x-1'
                }
              `}
              onMouseEnter={() => setHoveredItem(dataset.id)}
              onMouseLeave={() => setHoveredItem(null)}
              title={`Click to view ${dataset.name}`}
            >
              <div className="flex items-start space-x-3">
                {/* File Icon */}
                <div className="flex-shrink-0 mt-0.5">
                  <span className="text-white text-lg">
                    {dataset.original_filename.endsWith('.csv') ? '📊' :
                     dataset.original_filename.endsWith('.xlsx') || dataset.original_filename.endsWith('.xls') ? '📈' :
                     dataset.original_filename.endsWith('.json') ? '📋' : '📄'}
                  </span>
                </div>

                {/* Dataset Info */}
                <div className="flex-1 min-w-0">
                  <div className="text-white text-sm font-medium truncate">
                    {dataset.name}
                  </div>
                  <div className="text-white text-xs opacity-75 mt-1">
                    {dataset.rows_count ? (
                      <span>{dataset.rows_count} rows • {formatFileSize(dataset.file_size)}</span>
                    ) : (
                      <span>{formatFileSize(dataset.file_size)} • {dataset.processing_status}</span>
                    )}
                  </div>
                  <div className="text-white text-xs opacity-60 mt-0.5">
                    {formatDate(dataset.created_at)}
                  </div>
                </div>

                {/* Status Indicator */}
                <div className="flex-shrink-0">
                  <div className={`w-2 h-2 rounded-full ${
                    dataset.processing_status === 'processed'
                      ? 'bg-green-400'
                      : dataset.processing_status === 'processing'
                        ? 'bg-yellow-400 animate-pulse'
                        : 'bg-gray-400'
                  }`} />
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Empty State */}
      {datasets.length === 0 && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <span className="text-white text-4xl mb-4 block">📁</span>
            <p className="text-white text-sm opacity-75">
              No datasets uploaded yet
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoryPanel;
